stages:
  - build
  - test
  - deploy

variables:
  NODE_VERSION: "20"
  FIREBASE_HOSTING_URL: "https://drcr-d660a.web.app"

# Improved cache configuration with key based on package.json
cache:
  key:
    files:
      - package.json
  paths:
    - node_modules/
  policy: pull-push

build:
  stage: build
  image: node:${NODE_VERSION}
  before_script:
    # Force clean install to avoid native dependency issues
    - echo "Cleaning previous installation..."
    - rm -rf node_modules package-lock.json .npm
  script:   
    # Install dependencies and build
    - echo "Installing dependencies..."
    - npm install --no-audit
    - echo "Verifying Rollup native dependencies..."
    - npm ls @rollup/rollup-linux-x64-gnu || echo "Native binary not found, but continuing..."
    - echo "Building application..."
    - npm run build
    
    # Show build stats
    - echo "Build completed! Bundle analysis:"
    - find dist -type f -exec du -h {} \; | sort -hr | head -n 10
  artifacts:
    paths:
      - dist/
      - firebase.json
      - .firebaserc
    expire_in: 1 week
  cache:
    key:
      files:
        - package.json
    paths:
      - node_modules/
    policy: pull-push
  only:
    - main

lint:
  stage: test
  image: node:${NODE_VERSION}
  before_script:
    # Clean install for linting to ensure consistency
    - rm -rf node_modules package-lock.json .npm
    - npm install --no-audit
  script:
    - npm run lint --format=json --output-file=eslint-report.json || true
  artifacts:
    paths:
      - eslint-report.json
    reports:
      codequality: eslint-report.json
    when: always
    expire_in: 1 week
  cache:
    key:
      files:
        - package.json
    paths:
      - node_modules/
    policy: pull
  allow_failure: true
  only:
    - main

deploy_production:
  stage: deploy
  image: node:${NODE_VERSION}
  dependencies:
    - build
  before_script:
    # Only install Firebase CLI, not all dependencies
    - npm install -g firebase-tools
  script:
    - chmod +x ./ci/deploy-firebase.sh
    - ./ci/deploy-firebase.sh
  environment:
    name: production
    url: ${FIREBASE_HOSTING_URL}
  cache: {}  # No cache needed for deploy job
  only:
    - main