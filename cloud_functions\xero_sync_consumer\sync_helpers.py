import os
import logging
import asyncio
import re
from typing import Optional, Any
from google.cloud import storage

# Configure basic logging
logger = logging.getLogger(__name__)
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID")

async def _upload_to_gcs(
    bucket_name: str,
    destination_blob_name: str,
    file_bytes: bytes,
    content_type: Optional[str] = None,
):
    """Uploads bytes to a GCS bucket asynchronously."""
    try:
        storage_client = storage.Client(project=GCP_PROJECT_ID)
        loop = asyncio.get_running_loop()

        def upload_sync():
            bucket = storage_client.bucket(bucket_name)
            blob = bucket.blob(destination_blob_name)
            blob.upload_from_string(
                data=file_bytes,
                content_type=content_type or "application/octet-stream",
            )
            logger.info(
                f"Successfully uploaded to gs://{bucket_name}/{destination_blob_name}"
            )

        await loop.run_in_executor(None, upload_sync)
    except Exception as e:
        logger.error(
            f"Failed to upload gs://{bucket_name}/{destination_blob_name} to GCS: {e}",
            exc_info=True,
        )

def get_other_secret(secret_name_in_secret_manager: str) -> Optional[str]:
    """Fetches a generic secret from environment."""
    secret_value = os.getenv(secret_name_in_secret_manager.upper().replace("-", "_"))
    logger.info(
        f"Attempting to get secret: {secret_name_in_secret_manager}. Found: {'Yes' if secret_value else 'No'}"
    )
    if not secret_value:
        logger.warning(
            f"Secret {secret_name_in_secret_manager} not found in environment."
        )
    return secret_value

def _normalize_to_float(value: Any) -> Optional[float]:
    if value is None:
        return None
    try:
        normalized_str = re.sub(r"[^\d.\-]", "", str(value))
        if not normalized_str or normalized_str == "-":
            return None
        return float(normalized_str)
    except (ValueError, TypeError):
        logger.warning(f"Could not normalize '{value}' to float.")
        return None 