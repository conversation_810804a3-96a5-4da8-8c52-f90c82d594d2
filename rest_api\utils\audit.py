"""
Audit utilities for creating audit log entries
"""
from typing import Dict, Any, Optional
import uuid
from google.cloud.firestore import SERVER_TIMESTAMP


async def create_audit_log_entry(
    db,
    event_category: str,
    event_type: str,
    client_id: str,
    entity_id: str,
    status: str,
    details: Dict[str, Any],
    user_id: Optional[str] = None,
    source_ip: Optional[str] = None
):
    """Creates an audit log entry in Firestore."""
    try:
        log_entry_id = str(uuid.uuid4())
        log_data = {
            "timestamp": SERVER_TIMESTAMP,
            "event_category": event_category,
            "event_type": event_type,
            "client_id": client_id,
            "entity_id": entity_id,
            "user_id": user_id,
            "source_ip": source_ip,
            "status": status,
            "details": details
        }

        # Remove None fields
        log_data = {k: v for k, v in log_data.items() if v is not None}

        audit_log_ref = db.collection("AUDIT_LOG").document(log_entry_id)
        await audit_log_ref.set(log_data)
    except Exception as e:
        # Log error but don't fail the main operation
        print(f"Failed to create audit log: {e}") 