# DRCR Backend Documentation Index

## 📚 Main Documentation
- **Main README**: `docs/README.md` - Complete documentation overview
- **Codebase Structure**: `docs/development/codebase_structure.md` - Project organization guide
- **API Guide**: `docs/api_guide/README.md` - REST API documentation
- **Development Setup**: `docs/development/setup.md` - Environment setup instructions

## 🔄 Scheduled Sync System (Complete Implementation)

### Core Documentation
- **Comprehensive Guide**: `docs/SCHEDULED_SYNC_SYSTEM.md` - Full technical documentation
- **Quick Reference**: `docs/SCHEDULED_SYNC_QUICK_REFERENCE.md` - Developer cheat sheet
- **Test Results**: `docs/SCHEDULED_SYNC_TEST_RESULTS.md` - Verification and testing summary

### Infrastructure Components
- **Cloud Scheduler**: `terraform/scheduler.tf` - Automated sync triggers (hourly/daily/weekly)
- **Cloud Functions**: `terraform/functions.tf` - Function definitions and configuration
- **Pub/Sub Topics**: `terraform/pubsub.tf` - Message handling infrastructure
- **Main Terraform**: `terraform/main.tf` - Core infrastructure with GCS backend

### Cloud Functions
- **Scheduled Sync Processor**: `cloud_functions/scheduled_sync_processor/`
  - `main.py` - Queries entities by frequency and triggers syncs
  - `requirements.txt` - Dependencies for Firestore and Pub/Sub
- **Xero Sync Consumer**: `cloud_functions/xero_sync_consumer/`
  - `main.py` - Processes actual sync requests with organisation sync
  - `financial_doc_adapter.py` - Enhanced with async URL generation
  - `requirements.txt` - Xero API and Firestore dependencies

### REST API Integration
- **Entity Settings**: `rest_api/routes/entities.py` - Enhanced with automated sync triggers
  - Fixed transactionSyncStartDate inclusion
  - Automated sync on settings changes
  - Configuration status endpoints

## 🤖 LLM Integration System (Complete Implementation)

### Core Documentation
- **Data Flow & Storage Guide**: `docs/LLM_DATA_FLOW_AND_STORAGE.md` - Complete LLM data architecture
  - Storage collections (ATTACHMENTS vs TRANSACTIONS)
  - Decision logic for data placement
  - Field reference and troubleshooting guide
  - Common scenarios and examples
- **Prepayment Processing Logic**: `docs/LLM_PREPAYMENT_PROCESSING_LOGIC.md` - Confidence scoring and GL coding requirements
  - Combined decision matrix for automatic processing
  - Troubleshooting "0 prepayments processed" issues
  - Configuration examples and common fixes

### Core Components
- **Document Processor**: `drcr_shared_logic/document_processor.py` - LLM analysis functions
  - OpenAI direct processing with total validation
  - Mistral OCR fallback for complex documents
  - Service period extraction and confidence scoring
- **Xero Client Enhancement**: `drcr_shared_logic/clients/xero_client.py`
  - `get_attachments()` method for fetching attachment metadata
  - `download_attachment()` method for downloading content
  - Proper transaction type handling
- **LLM Utils**: `cloud_functions/xero_sync_consumer/llm_utils.py` - Processing logic
  - Attachment fetching and LLM processing
  - Combined prepayment analysis
  - Data storage decision logic

### Sync Integration
- **Combined Analysis**: GL coding + LLM prepayment detection
- **Attachment Processing**: Automatic fetching and analysis during sync
- **Service Period Extraction**: LLM-based service date detection
- **Confidence Scoring**: Quality control for LLM results
- **Entity Configuration**: `enable_llm_prepayment_detection` setting

### Testing & Verification
- **LLM Integration Tests**: `tests/test_llm_integration.py` - Comprehensive LLM testing
- **Simple Verification**: `tests/simple_llm_verification.py` - Quick LLM function tests
- **Full Sync Testing**: Verified with real Pub/Sub messages and 41 bills processed

## 👥 User Management System (Complete Implementation)

### Core Features
- **Complete CRUD Operations**: List, view, update, and remove users
- **Role-based Access Control**: Firm admin and staff role management
- **Client Assignment Management**: Granular access control per client
- **Status Management**: Activate, deactivate, suspend users
- **Firebase Integration**: Syncs with Firebase Auth for authentication
- **Invitation System**: Dedicated invitation emails with 24-hour expiry
- **Resend Invitations**: Ability to resend invites to users with "invited" status

### Backend Endpoints
- **List Users**: `GET /auth/users` - List all firm users with pagination and Firebase auth data
- **User Details**: `GET /auth/users/{user_id}` - Detailed user information including assigned clients
- **Update Role**: `PUT /auth/users/{user_id}/role` - Change user role and client assignments
- **Update Status**: `PUT /auth/users/{user_id}/status` - Activate/deactivate users (syncs with Firebase Auth)
- **Remove User**: `DELETE /auth/users/{user_id}` - Remove user from firm
- **Invite User**: `POST /auth/invite-user` - Invite new users with dedicated invitation emails
- **Resend Invite**: `POST /auth/users/{user_id}/resend-invite` - Resend invitation to users with "invited" status

### Email System
- **Separate Email Templates**: Distinct emails for invitations vs password resets
- **Invitation Emails**: Welcome messaging with firm context and account setup instructions
- **Password Reset Emails**: Standard password recovery messaging
- **SendGrid Integration**: Custom HTML emails with proper branding
- **24-Hour Expiry**: All invitation tokens expire after 24 hours
- **Firm Personalization**: Emails include actual firm name and inviter information

### Security Features
- **Admin Protection**: Cannot remove last firm admin
- **Self-Protection**: Cannot demote yourself if you're the only admin
- **Firm Isolation**: Users can only manage users within their own firm
- **Client Validation**: Validates client assignments before updating
- **Firebase Sync**: Updates both Firestore and Firebase Auth status
- **Token Security**: Secure password reset tokens for account setup

### Frontend Implementation
- **Complete Admin Interface**: Modern UI with user management dashboard
- **User Statistics**: Total users, active users, admins, pending invites
- **Searchable User Table**: Role badges, status indicators, and action dropdowns
- **Invitation Dialog**: Role assignment and client selection for new users
- **Resend Invite Option**: Available in dropdown menu for users with "invited" status
- **Real-time Updates**: Immediate UI updates after all actions
- **Toast Notifications**: Success/error feedback for all operations

### Data Model Integration
- **FIRM_USERS Collection**: Complete user profile management
- **Firebase Auth**: Authentication and account status
- **Client Assignments**: Granular access control per client
- **Audit Trail**: Created/updated timestamps and last_invite_sent tracking
- **Status Tracking**: invited, active, inactive, suspended states

### Testing & Documentation
- **Test Script**: `test_user_management.py` - Endpoint validation and documentation
- **API Documentation**: Complete request/response schemas
- **Error Handling**: Comprehensive validation and error messages

## 🏗️ Infrastructure Overview

### Terraform Files
```
terraform/
├── main.tf           # Core infrastructure, GCS backend, APIs
├── scheduler.tf      # Cloud Scheduler jobs
├── functions.tf      # Cloud Functions definitions
├── pubsub.tf         # Pub/Sub topics and subscriptions
├── api.tf            # Cloud Run API configuration
└── variables.tf      # Variable definitions
```

### Cloud Functions
```
cloud_functions/
├── scheduled_sync_processor/     # Automated sync processing
│   ├── main.py                  # Entity querying and sync triggering
│   └── requirements.txt         # Firestore + Pub/Sub dependencies
└── xero_sync_consumer/          # Main sync processor with LLM integration
    ├── main.py                  # Organisation sync + LLM processing
    ├── financial_doc_adapter.py # Async URL generation with caching
    └── requirements.txt         # Xero API + LLM dependencies
```

### Testing Structure
```
tests/
├── scheduled_sync/              # Dedicated sync testing
│   └── test_scheduled_sync.py   # Comprehensive test suite
├── test_llm_integration.py      # LLM integration testing
└── simple_llm_verification.py   # Quick LLM verification
```

### Scripts Organization
```
scripts/
├── deploy_scheduled_sync.py     # Infrastructure deployment
└── utilities/                   # Helper scripts (organized)
    ├── create_firestore_indexes.py
    ├── check_prepayment_data.py
    ├── pull_xero_invoices_for_testing.py
    └── sync_invoices_for_prepayment_testing.py
```

## 🎯 Key Features Implemented

### Automated Sync System
- **Cloud Scheduler Jobs**: Hourly (`0 * * * *`), Daily (`0 2 * * *`), Weekly (`0 3 * * 1`)
- **Smart Sync Logic**: Checks last sync timestamps to avoid unnecessary syncing
- **Entity Configuration**: Respects sync_frequency and auto_sync_enabled settings
- **Document Type Control**: Selective syncing based on entity preferences

### Enhanced Xero Integration
- **Organisation Sync**: Automatically fetches and stores Xero organisation details
- **Proper Deep Links**: Generates correct Xero URLs with organisation shortcode
- **URL Generation**: Async caching for performance
- **Comprehensive Sync**: All 6 document types (Bills, Invoices, SpendMoney, etc.)

### LLM-Powered Prepayment Detection
- **Dual Detection**: GL coding + LLM attachment analysis
- **Attachment Processing**: Automatic fetching and analysis from Xero
- **Service Period Extraction**: AI-powered date detection from invoices
- **Quality Control**: Total validation and confidence scoring
- **Fallback Logic**: OpenAI → Mistral OCR for complex documents

### Infrastructure Improvements
- **GCS Backend**: Terraform state management in Google Cloud Storage
- **Pub/Sub Architecture**: Reliable message handling with dead letter queues
- **Error Handling**: Comprehensive retry policies and error tracking
- **Monitoring**: Cloud Function logs and Pub/Sub metrics

## 🚀 Quick Commands

### Deploy Everything
```bash
python scripts/deploy_scheduled_sync.py
```

### Test System
```bash
python tests/scheduled_sync/test_scheduled_sync.py
python tests/test_llm_integration.py
```

### Monitor Logs
```bash
gcloud functions logs read scheduled-sync-processor --limit=20
gcloud functions logs read xero-sync-consumer --limit=20
```

### Check Infrastructure
```bash
gcloud scheduler jobs list
gcloud pubsub topics list
gcloud functions list
```

## 🔧 Configuration Files

### Entity Settings (Firestore)
```json
{
  "sync_frequency": "daily|hourly|weekly|manual",
  "auto_sync_enabled": true,
  "sync_bills": true,
  "sync_invoices": false,
  "enable_llm_prepayment_detection": true,
  "prepayment_asset_account_codes": ["620"],
  "transaction_sync_start_date": "2025-02-01",
  "_system_lastSyncTimestampUtc_Bills": "2025-06-03T13:30:07.783169+00:00"
}
```

### Environment Variables
```bash
GCP_PROJECT_ID=drcr-d660a
PUBSUB_TOPIC_XERO_SYNC=xero-sync-topic
OPENAI_API_KEY=your_openai_key
MISTRAL_API_KEY=your_mistral_key
```

## 📋 File Status Summary

### ✅ Implemented & Tested
- Complete scheduled sync infrastructure
- Cloud Scheduler integration
- Enhanced Xero sync with organisation support
- LLM-powered prepayment detection with attachment processing
- Proper URL generation for all document types
- Comprehensive testing suite
- Automated deployment scripts

### 🎨 Frontend Navigation Improvements (January 2025)
- **Enhanced Navigation UX**: Clickable DRCR logo, improved breadcrumbs
- **Sidebar Optimization**: Removed redundant Dashboard menu item
- **Firm Name Integration**: Breadcrumbs now display actual firm names instead of "DRCR"
- **Backend API Support**: New `/firms/{firm_id}` endpoints for firm data
- **Consistent Patterns**: Standardized navigation across all pages
- **Documentation**: `docs/NAVIGATION_IMPROVEMENTS_SUMMARY.md`

### 🧹 Project Organization
- **Root Directory**: Clean and organized, only essential files
- **Tests**: Organized in `tests/` directory with dedicated subdirectories
- **Utilities**: Organized in `scripts/utilities/`
- **Documentation**: Complete with quick reference guides
- **Temporary Files**: All cleaned up and removed

### 📁 Clean Directory Structure
```
DRCR Backend/
├── cloud_functions/         # Cloud Functions code
├── drcr_shared_logic/       # Shared business logic
├── docs/                    # Complete documentation
├── rest_api/               # REST API implementation
├── scripts/                # Deployment and utility scripts
├── terraform/              # Infrastructure as code
├── tests/                  # All test files organized
├── README.md               # Main project documentation
└── [core project files]    # Essential configuration files only
```

For questions about documentation or to suggest improvements, contact the development team. 