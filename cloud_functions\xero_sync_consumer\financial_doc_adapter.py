import logging
from datetime import datetime
from google.cloud import firestore

class FinancialDocAdapter:
    """
    Adapter for processing financial documents from Xero into a standardized format.
    """
    
    def __init__(self, entity_id: str = None):
        """Initialize the financial document adapter."""
        self.logger = logging.getLogger(__name__)
        self.entity_id = entity_id
        self._cached_shortcode = None
    
    async def _get_xero_shortcode(self) -> str:
        """
        Fetch the Xero shortcode from the ENTITIES collection.
        
        Returns:
            The Xero shortcode or None if not found
        """
        if self._cached_shortcode:
            return self._cached_shortcode
            
        if not self.entity_id:
            self.logger.warning("No entity_id provided, cannot fetch shortcode")
            return None
            
        try:
            db = firestore.Client()
            entity_ref = db.collection("ENTITIES").document(self.entity_id)
            entity_doc = entity_ref.get()
            
            if entity_doc.exists:
                entity_data = entity_doc.to_dict()
                shortcode = entity_data.get("xero_short_code")
                if shortcode:
                    self._cached_shortcode = shortcode
                    return shortcode
                else:
                    self.logger.warning(f"No xero_short_code found for entity_id: {self.entity_id}")
            else:
                self.logger.error(f"Entity document not found for entity_id: {self.entity_id}")
        except Exception as e:
            self.logger.error(f"Error fetching shortcode for entity_id {self.entity_id}: {e}")
        
        return None
    
    async def process_invoices(self, invoices):
        """
        Process Xero invoices into a standardized format.
        
        Args:
            invoices: List of invoice objects from Xero API
            
        Returns:
            List of standardized invoice objects
        """
        processed_invoices = []
        
        for invoice in invoices:
            try:
                processed_invoice = await self._transform_invoice(invoice)
                processed_invoices.append(processed_invoice)
            except Exception as e:
                self.logger.error(f"Error processing invoice {invoice.get('InvoiceID')}: {e}")
        
        return processed_invoices
    
    async def _transform_invoice(self, invoice):
        """
        Transform a Xero invoice into the standardized format.
        
        Args:
            invoice: Invoice object from Xero API
            
        Returns:
            Standardized invoice object
        """
        # Extract basic invoice information
        invoice_id = invoice.get("InvoiceID")
        invoice_number = invoice.get("InvoiceNumber")
        
        # Map status
        status_map = {
            "DRAFT": "draft",
            "SUBMITTED": "submitted",
            "AUTHORISED": "authorized",
            "PAID": "paid",
            "VOIDED": "voided",
            "DELETED": "deleted"
        }
        status = status_map.get(invoice.get("Status"), "unknown")
        
        # Format dates
        date_format = "%Y-%m-%d"
        date_issued = self._parse_date(invoice.get("Date"), date_format)
        date_due = self._parse_date(invoice.get("DueDate"), date_format)
        
        # Extract contact information
        contact = invoice.get("Contact", {})
        contact_id = contact.get("ContactID")
        contact_name = contact.get("Name")
        
        # Process line items
        line_items = []
        for item in invoice.get("LineItems", []):
            line_item = {
                "description": item.get("Description", ""),
                "quantity": item.get("Quantity", 0),
                "unit_amount": item.get("UnitAmount", 0),
                "tax_amount": item.get("TaxAmount", 0),
                "line_amount": item.get("LineAmount", 0),
                "account_code": item.get("AccountCode", ""),
                "tax_type": item.get("TaxType", "")
            }
            line_items.append(line_item)
        
        # Create standardized invoice
        standardized_invoice = {
            "source": "xero",
            "source_id": invoice_id,
            "document_number": invoice_number,
            "status": status,
            "type": "invoice" if invoice.get("Type") == "ACCREC" else "bill",
            "date_issued": date_issued,
            "date_due": date_due,
            "currency": invoice.get("CurrencyCode", ""),
            "contact": {
                "id": contact_id,
                "name": contact_name
            },
            "line_items": line_items,
            "subtotal": invoice.get("SubTotal", 0),
            "tax_total": invoice.get("TotalTax", 0),
            "total": invoice.get("Total", 0),
            "amount_paid": invoice.get("AmountPaid", 0),
            "amount_due": invoice.get("AmountDue", 0),
            "metadata": {
                "xero_updated_date": invoice.get("UpdatedDateUTC"),
                "xero_url": await self._generate_xero_url(invoice_id, invoice.get("Type"))
            }
        }
        
        return standardized_invoice
    
    def _parse_date(self, date_str, format_str):
        """
        Parse a date string into the specified format.
        
        Args:
            date_str: Date string to parse
            format_str: Format string for the output
            
        Returns:
            Formatted date string or None if parsing fails
        """
        if not date_str:
            return None
        
        try:
            # Handle Xero's /Date(timestamp+timezone)/ format
            if "/Date(" in date_str:
                # Extract timestamp from /Date(1618876800000+0000)/
                date_part = date_str.split("(")[1].split(")")[0]
                # Remove timezone offset (everything after + or -)
                if "+" in date_part:
                    timestamp_str = date_part.split("+")[0]
                elif "-" in date_part and date_part.count("-") > 0:
                    # Handle negative timezone, but be careful not to split negative timestamps
                    parts = date_part.split("-")
                    if len(parts) > 1 and parts[-1].isdigit() and len(parts[-1]) == 4:
                        timestamp_str = "-".join(parts[:-1])
                    else:
                        timestamp_str = date_part
                else:
                    timestamp_str = date_part
                
                timestamp = int(timestamp_str)
                date_obj = datetime.fromtimestamp(timestamp / 1000)  # Convert milliseconds to seconds
            else:
                # Try to parse as ISO format
                date_obj = datetime.fromisoformat(date_str.replace("Z", "+00:00"))
            
            return date_obj.strftime(format_str)
        except Exception as e:
            self.logger.warning(f"Error parsing date {date_str}: {e}")
            return None

    async def _generate_xero_url(self, invoice_id, invoice_type):
        """
        Generate a Xero URL based on the invoice type.
        
        Args:
            invoice_id: Invoice ID
            invoice_type: Invoice type (ACCREC or ACCPAY)
            
        Returns:
            Generated Xero URL
        """
        shortcode = await self._get_xero_shortcode()
        if not shortcode:
            self.logger.warning(f"No shortcode available for entity_id {self.entity_id}, cannot generate deep link")
            return None
            
        if invoice_type == "ACCREC":
            redirect_url = f"/AccountsReceivable/View.aspx?InvoiceID={invoice_id}"
        elif invoice_type == "ACCPAY":
            redirect_url = f"/AccountsPayable/Edit.aspx?InvoiceID={invoice_id}"
        else:
            self.logger.warning(f"Unknown invoice type: {invoice_type}")
            return None
            
        # Generate proper Xero deep link with organisation login
        return f"https://go.xero.com/organisationlogin/default.aspx?shortcode={shortcode}&redirecturl={redirect_url}"
