# Codebase Structure

Understanding the organization of the DRCR backend codebase is key to navigating and contributing effectively. This document provides an overview of the main directories and their purposes.

A well-defined structure helps in maintaining modularity, separation of concerns, and ease of locating specific pieces of functionality.

## Main Project Directories

The DRCR backend project is organized into the following primary directories at the root level:

*   **`rest_api/`**
    *   **Purpose**: Contains all code related to the FastAPI-based REST API.
    *   **Key Contents**:
        *   `main.py`: The primary FastAPI application entry point and configuration.
        *   `app.py`: Alternative FastAPI application setup (legacy/backup).
        *   `run_server.py`: Development server runner script.
        *   `routes/`: Directory containing API route definitions, organized by resource:
            *   `auth.py`: Authentication and authorization endpoints
            *   `clients.py`: Client management endpoints
            *   `entities.py`: Entity configuration and settings endpoints
            *   `xero.py`: Xero integration and OAuth endpoints
            *   `transactions.py`: Transaction processing endpoints
            *   `schedules.py`: Amortization schedule endpoints
            *   `invoices.py`: Invoice management endpoints
            *   `reports.py`: Reporting endpoints
        *   `models/`: Pydantic models for request/response validation and schemas.
        *   `schemas/`: Additional schema definitions and data structures.
        *   `services/`: Business logic layer services.
        *   `core/`: Core application components and utilities.
        *   `local_utils/`: Utility functions specific to the API.
        *   `dependencies.py`: Common API dependencies (e.g., for authentication, database sessions).
        *   `Dockerfile`: Instructions for building the Docker image for the REST API.
        *   `requirements.txt`: API-specific Python dependencies.

*   **`cloud_functions/`**
    *   **Purpose**: Houses the source code for all Google Cloud Functions.
    *   **Key Contents**:
        *   `xero_sync_consumer/`: Processes Xero synchronization requests from Pub/Sub
            *   `main.py`: Main sync processing logic with comprehensive document type handling
            *   `requirements.txt`: Dependencies for Xero API integration and Firestore operations
        *   `scheduled_sync_processor/`: Handles automated scheduled syncing based on entity frequencies
            *   `main.py`: Queries entities by sync frequency and triggers individual syncs
            *   `requirements.txt`: Dependencies for Firestore queries and Pub/Sub publishing
        *   Each subdirectory typically contains:
            *   `main.py`: The entry point for the cloud function.
            *   `requirements.txt`: Specific dependencies for that function.
            *   Helper modules and logic specific to the function.
        *   `index.js` or `package.json` might be present if functions are written in Node.js or require Node.js tooling for deployment via Firebase CLI, even if the core logic is Python.

*   **`drcr_shared_logic/`**
    *   **Purpose**: Contains core business logic, data access layers, service utilities, and other components that are shared across different parts of the application (e.g., by both the REST API and Cloud Functions).
    *   **Key Contents**:
        *   `database/`: Modules for interacting with Firestore (e.g., CRUD operations, complex queries).
        *   `services/`: Business logic services (e.g., `amortization_service.py`, `xero_service.py`).
        *   `models/`: Core data models or dataclasses (distinct from API Pydantic models, though they might be related or used for internal representation).
        *   `local_utils/`: Common utility functions used across the backend.
        *   `config.py`: Application configuration loading and management.

*   **`terraform/`**
    *   **Purpose**: Contains Infrastructure as Code (IaC) scripts, primarily using Terraform, to define and manage cloud resources on Google Cloud Platform (e.g., Cloud Run services, Cloud Functions deployments, Firestore databases, Pub/Sub topics).
    *   **Key Contents**:
        *   `main.tf`: Core infrastructure configuration with GCS backend and enabled APIs
        *   `functions.tf`: Cloud Functions definitions for sync processing
        *   `pubsub.tf`: Pub/Sub topics and subscriptions for message handling
        *   `scheduler.tf`: Cloud Scheduler jobs for automated sync triggers
        *   `api.tf`: Cloud Run API service configuration
        *   `variables.tf`: Terraform variable definitions
        *   Modules for organizing Terraform code.

*   **`tests/`**
    *   **Purpose**: Contains all automated tests for the application.
    *   **Key Contents**:
        *   `unit/`: Unit tests for individual modules and functions.
        *   `integration/`: Integration tests that verify interactions between different components or with emulated services.
        *   `api/`: API integration and functional tests for various endpoints and workflows.
        *   `performance/`: Performance testing scripts for API endpoints and optimization validation.
        *   `token_management/`: OAuth token management tests and utilities for Firestore token storage.
        *   `xero_integration/`: Xero-specific integration tests and manual testing tools.
        *   `cloud_functions/`: Tests for Google Cloud Functions.
        *   `rest_api/`: REST API specific tests.
        *   `python/`: Python-specific test utilities and helpers.
        *   `powershell/`: PowerShell test scripts for Windows environments.
        *   `utils/`: Test utility files, fixtures, and mock data.
        *   Test configuration files and authentication tokens for testing.

*   **`scripts/`**
    *   **Purpose**: Utility and deployment scripts for development and production workflows.
    *   **Key Contents**:
        *   `utilities/`: Development utilities (encryption key generation, configuration fixes, token generation).
        *   `deployment/`: Deployment and server management scripts (fast startup, production deployment, cleanup).
        *   `deploy_scheduled_sync.py`: Automated deployment script for scheduled sync infrastructure.

*   **`deployment/`**
    *   **Purpose**: Infrastructure and deployment configurations.
    *   **Key Contents**:
        *   Deployment configuration files and scripts.
        *   Environment-specific settings.

*   **`ci/`**
    *   **Purpose**: Continuous Integration and Continuous Deployment (CI/CD) scripts.
    *   **Key Contents**:
        *   GitLab CI/CD pipeline scripts.
        *   Build and deployment automation scripts.

*   **`local_utils/`**
    *   **Purpose**: General utility functions and helpers used across the project.

*   **`shared_lib/`**
    *   **Purpose**: Legacy shared library components (may be deprecated in favor of `drcr_shared_logic/`).

*   **`docs/`**
    *   **Purpose**: Contains all project documentation, including this Development Guide, the API Guide, Data Model documentation, etc.
    *   **Key Contents**:
        *   `api_guide/`: API endpoints, authentication, and router configuration.
        *   `data_model/`: Firestore collections and relationships.
        *   `development/`: Setup instructions, testing guidelines, and codebase structure.
        *   `error_handling/`: Error codes and response formats.
        *   `SCHEDULED_SYNC_SYSTEM.md`: Comprehensive documentation for automated sync infrastructure.
        *   Performance optimization guides and CI/CD documentation.

## Root Level Files

Notable files typically found at the project root include:

*   **`.env`**: Environment variables for local development (not in version control).
*   **`requirements.txt`**: Main Python dependencies for the project.
*   **`README.md`**: The main project README providing a comprehensive overview.
*   **`.gitignore`**: Specifies intentionally untracked files that Git should ignore.
*   **`.gcloudignore`**: Files to ignore during Google Cloud deployment.
*   **`.gitlab-ci.yml`**: GitLab CI/CD pipeline configuration.
*   **`Dockerfile`**: Container configuration for Cloud Run deployment.
*   **`drcr-d660a-firebase-adminsdk-fbsvc-f1d2dc57df.json`**: Firebase service account credentials.
*   **Test files**: Various test scripts and utilities in the root directory:
    *   `test_*.py`: Python test scripts for specific functionality.
    *   `test_scheduled_sync.py`: Comprehensive testing for scheduled sync functionality.
    *   `check_*.py`: Data verification and debugging scripts.
    *   `process_pubsub_message.py`: Pub/Sub message processing utilities.
*   **Performance and monitoring files**:
    *   `performance_results_*.json`: Performance test results.
    *   Various debugging and configuration files.

---

This structure aims to provide a clear separation of concerns. When looking for specific code:
*   For API endpoint definitions or request/response models, look in `rest_api/`.
*   For serverless background tasks or event-driven logic, check `cloud_functions/`.
*   For shared business rules, database interactions, or core utilities, explore `drcr_shared_logic/`.
*   For infrastructure definitions, see `terraform/`.
*   For tests, go to `tests/`.
*   For scheduled sync system components, see `cloud_functions/scheduled_sync_processor/`, `terraform/scheduler.tf`, and `docs/SCHEDULED_SYNC_SYSTEM.md`.