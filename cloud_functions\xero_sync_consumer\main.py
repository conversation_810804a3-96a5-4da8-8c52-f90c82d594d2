# cloud_functions/xero_sync_consumer/main.py
import os
import sys # Ensure sys is imported

# Calculate the project root directory (drcr_back)
# __file__ is drcr_back/cloud_functions/xero_sync_consumer/main.py
# os.path.dirname(__file__) is drcr_back/cloud_functions/xero_sync_consumer
# os.path.join(os.path.dirname(__file__), '..') is drcr_back/cloud_functions
# os.path.join(os.path.dirname(__file__), '..', '..') is drcr_back
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
if project_root not in sys.path:
    sys.path.insert(0, project_root) # Add project_root to the beginning of the search path

import base64 # Keep standard library imports first if preferred
import json
import asyncio
import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone, date
from dateutil.relativedelta import relativedelta
from io import BytesIO
import re

from dotenv import load_dotenv
load_dotenv()  # Load .env file as early as possible

from google.cloud import storage
from google.cloud import firestore
import httpx

# UPDATED IMPORTS for shared logic:
from drcr_shared_logic.clients.base_client import BaseAccountingClient
from drcr_shared_logic.clients.xero_client import XeroApiClient
from drcr_shared_logic.document_processor import (
    extract_data_with_openai_direct,
    is_direct_openai_attempt_sufficient,
    process_pdf_with_mistral_ocr,
    process_image_with_mistral_ocr,
    extract_structured_data_with_openai,
    post_process_extracted_invoice_data,
)

# NEW IMPORTS FOR REFACTORED HELPERS (Changed to absolute-style)
from cloud_functions.xero_sync_consumer.sync_helpers import _upload_to_gcs, get_other_secret, _normalize_to_float
from cloud_functions.xero_sync_consumer.llm_utils import _fetch_and_process_attachments, _perform_combined_prepayment_analysis
# END NEW IMPORTS

# Load secrets from Secret Manager in production (if enabled)
try:
    from drcr_shared_logic.config.secret_loader import load_secrets_at_startup
    SECRET_LOADER_AVAILABLE = True
except ImportError:
    SECRET_LOADER_AVAILABLE = False

# Initialize secrets loading
if SECRET_LOADER_AVAILABLE and os.getenv("LOAD_SECRETS_FROM_SECRET_MANAGER", "false").lower() == "true":
    try:
        success = load_secrets_at_startup()
        if success:
            print("Cloud Function: Successfully loaded secrets from Secret Manager")
        else:
            print("Cloud Function: Some secrets failed to load from Secret Manager - continuing with environment variables")
    except Exception as e:
        print(f"Cloud Function: Failed to load secrets from Secret Manager: {e}")
        print("Cloud Function: Continuing with environment variables...")
else:
    print("Cloud Function: Using environment variables for secrets")

# Configure basic logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(name)s - %(message)s"
)
logger = logging.getLogger(__name__)

# GCP Project ID (will be an environment variable in GCP)
# This is used by XeroApiClient for Secret Manager token storage if not overridden in its config.
GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID")


# --- Audit Log Helper Function ---
async def _create_audit_log_entry(
    db: firestore.AsyncClient,
    event_category: str,
    event_type: str,
    client_id: Optional[str],
    entity_id: str,
    status: str, # e.g., "SUCCESS", "FAILURE", "STARTED", "INFO"
    details: Dict[str, Any],
    user_id: Optional[str] = None,
    source_ip: Optional[str] = None, # Optional: for user-initiated actions via API
):
    """Creates an audit log entry in Firestore."""
    try:
        log_entry_id = str(uuid.uuid4())
        log_data = {
            "timestamp": firestore.SERVER_TIMESTAMP,
            "eventCategory": event_category, # e.g., "SYNC", "USER_MANAGEMENT", "SYSTEM"
            "eventType": event_type,         # e.g., "XERO_SYNC_JOB_STARTED", "USER_LOGIN_SUCCESS"
            "clientId": client_id,
            "entityId": entity_id,
            "userId": user_id,
            "sourceIp": source_ip,
            "status": status,
            "details": details, # A dictionary for structured information
        }
        # Remove None fields to keep documents clean
        log_data = {k: v for k, v in log_data.items() if v is not None}

        audit_log_ref = db.collection("AUDIT_LOG").document(log_entry_id)
        await audit_log_ref.set(log_data)
        logger.info(f"Audit log created: {event_category} - {event_type} for entity {entity_id}. ID: {log_entry_id}")
    except Exception as e_audit:
        logger.error(f"Failed to create audit log for {event_category} - {event_type} for entity {entity_id}: {e_audit}", exc_info=True)
# --- End Audit Log Helper Function ---


# --- Firestore Sync Timestamp Helper (Initial Version) ---
async def _update_firestore_sync_timestamp(
    db: firestore.AsyncClient,
    entity_id: str, # Changed from platform_org_id
    sync_endpoint: str,
    timestamp_to_set: str,
    reason: str,
):
    """Updates the specific last sync timestamp in ENTITY_SETTINGS for an entity and endpoint."""
    try:
        entity_settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id) # Use entity_id
        sync_timestamp_field_name = f"_system_lastSyncTimestampUtc_{sync_endpoint.replace('/', '_').lstrip('_')}"
        update_data = {
            sync_timestamp_field_name: timestamp_to_set,
            "_system_lastSyncTimestampUtc_reason": reason,
            "_system_updatedAt": firestore.SERVER_TIMESTAMP,
        }
        await entity_settings_ref.set(update_data, merge=True)
        logger.info(
            f"Successfully updated sync timestamp field {sync_timestamp_field_name} to {timestamp_to_set} for entity {entity_id} due to: {reason}."
        )
    except Exception as e_fs_ts_update:
        logger.error(
            f"Failed to update {sync_timestamp_field_name} for entity {entity_id} to {timestamp_to_set}: {e_fs_ts_update}",
            exc_info=True,
        )


async def get_accounting_client(
    platform: str, entity_id: str, client_id: Optional[str] # Changed platform_org_id to entity_id, tenant_id to client_id
) -> BaseAccountingClient:
    """
    Factory function to instantiate and return the appropriate accounting client.
    """
    # Configurations for each client can be loaded from env vars or a config file/service
    # These are passed to the client constructor.
    client_config: Dict[str, Any] = {
        "GCP_PROJECT_ID": GCP_PROJECT_ID,
        "XERO_CLIENT_ID": os.getenv("XERO_CLIENT_ID"),
        "XERO_CLIENT_SECRET": os.getenv("XERO_CLIENT_SECRET"),
        "XERO_REDIRECT_URI": os.getenv("XERO_REDIRECT_URI_REST_API"),
        "XERO_SCOPES": os.getenv(
            "XERO_SCOPES", "accounting.transactions,accounting.settings,offline_access"
        ),
        "XERO_OAUTH_SECRET_NAME": os.getenv(
            "XERO_OAUTH_SECRET_NAME_PREFIX", "xero-oauth-tokens"
        )
        + f"_{entity_id}", # Use entity_id
    }

    if platform.lower() == "xero":
        logger.info(f"Creating XeroApiClient for entity_id: {entity_id}")
        # XeroApiClient's create method expects platform_org_id and tenant_id
        # We map our standardized entity_id and client_id to these expected parameters
        client = await XeroApiClient.create( 
            platform_org_id=entity_id, 
            tenant_id=client_id, 
            config=client_config
        )
        return client
    else:
        error_msg = f"Unsupported accounting platform: {platform}"
        logger.error(error_msg)
        raise ValueError(error_msg)


# Helper function to parse date strings robustly
def _parse_date_string_to_date(date_string: Optional[str]) -> Optional[date]:
    if not date_string:
        return None
    try:
        # Xero often uses ISO 8601 format like "YYYY-MM-DDTHH:mm:ss"
        # We only need the date part.
        return datetime.fromisoformat(date_string.replace("Z", "+00:00")).date()
    except ValueError:
        try:
            # Attempt other common formats if ISO fails, e.g., "YYYY-MM-DD"
            return datetime.strptime(date_string, "%Y-%m-%d").date()
        except ValueError:
            logger.warning(f"Could not parse date string: {date_string}")
            return None


async def xero_sync_consumer(event, context):
    logger.info(f"Received Pub/Sub message event: {event}")
    if not event or "data" not in event:
        logger.error("Invalid Pub/Sub message: Missing data field.")
        return

    # Initialize db to None to handle cases where it's not created before an error
    db = None

    try:
        message_data_str = base64.b64decode(event["data"]).decode("utf-8")
        message_data = json.loads(message_data_str)
        logger.info(f"Decoded message data: {message_data}")
    except Exception as e:
        logger.error(f"Error decoding Pub/Sub message data: {e}", exc_info=True)
        return

    # Standardize IDs from message payload
    entity_id = message_data.get("platformOrgId") or message_data.get("xeroTenantId")
    client_id = message_data.get("tenantId") 
    
    if not entity_id:
        logger.error(f"Missing platformOrgId/xeroTenantId (mapped to entity_id) in message data: {message_data}")
        # db initialization for audit log
        db_for_audit = firestore.AsyncClient(project=GCP_PROJECT_ID)
        try:
            await _create_audit_log_entry(
                db_for_audit, "SYNC", "XERO_SYNC_JOB_FAILURE", client_id, "UNKNOWN_ENTITY", "FAILURE", 
                {"error": "Missing entity_id in PubSub message", "message_data": message_data}
            )
        finally:
            await db_for_audit.close()
        return

    if not client_id:
        # Attempt to fetch client_id from entity_settings later if not provided in message
        logger.warning(f"Missing tenantId (mapped to client_id) in message data for entity_id {entity_id}. Will attempt to retrieve from settings.")

    sync_job_id = message_data.get("syncJobId", str(uuid.uuid4()))
    requested_endpoints = message_data.get("endpoints", [])
    force_full_sync_endpoints = message_data.get("forceFullSyncEndpoints", [])
    target_date_str = message_data.get("targetDate")
    target_date = _parse_date_string_to_date(target_date_str) if target_date_str else date.today()

    db = firestore.AsyncClient(project=GCP_PROJECT_ID)

    try:
        await _create_audit_log_entry(
            db, "SYNC", "XERO_SYNC_JOB_STARTED", client_id, entity_id, "STARTED", 
            {"syncJobId": sync_job_id, "requestedEndpoints": requested_endpoints, "forceFullSync": force_full_sync_endpoints}
        )

        entity_settings_ref = db.collection("ENTITY_SETTINGS").document(entity_id)
        entity_settings_doc = await entity_settings_ref.get()
        if not entity_settings_doc.exists:
            logger.error(f"Entity settings not found for entity_id: {entity_id}. Cannot proceed.")
            await _create_audit_log_entry(
                db, "SYNC", "XERO_SYNC_JOB_FAILURE", client_id, entity_id, "FAILURE", 
                {"error": "Entity settings not found", "syncJobId": sync_job_id}
            )
            return
        
        entity_settings = entity_settings_doc.to_dict()
        
        if not client_id and entity_settings.get("clientId"):
            client_id = entity_settings["clientId"] # Retrieve client_id from settings
            logger.info(f"Retrieved client_id '{client_id}' from entity_settings for entity_id '{entity_id}'.")
        elif not client_id: # Still no client_id
             logger.warning(f"client_id could not be determined for entity_id {entity_id}. Some operations might be affected.")


        xero_client = await get_accounting_client(
            platform="xero", entity_id=entity_id, client_id=client_id # Pass standardized IDs
        )
        
        if not await xero_client.is_authenticated():
            logger.error(f"Xero client authentication failed for entity_id: {entity_id}.")
            await _create_audit_log_entry(
                db, "SYNC", "XERO_AUTHENTICATION_FAILURE", client_id, entity_id, "FAILURE", 
                {"error": "Xero client authentication failed", "syncJobId": sync_job_id}
            )
            return

        # Organisation Sync - Fetch and store critical organisation details
        logger.info(f"Starting Organisation sync for entity_id: {entity_id}, client_id: {client_id}")
        try:
            org_details = await xero_client.get_organisation_details()
            if org_details:
                # Update ENTITIES collection with organisation details
                entity_ref = db.collection("ENTITIES").document(entity_id)
                entity_update = {
                    "xero_organisation_id": org_details.get("OrganisationID"),
                    "xero_short_code": org_details.get("ShortCode"),
                    "base_currency": org_details.get("BaseCurrency"),
                    "country_code": org_details.get("CountryCode"),
                    "timezone": org_details.get("Timezone"),
                    "financial_year_end_day": org_details.get("FinancialYearEndDay"),
                    "financial_year_end_month": org_details.get("FinancialYearEndMonth"),
                    "organisation_status": org_details.get("OrganisationStatus"),
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                # Remove None values
                entity_update = {k: v for k, v in entity_update.items() if v is not None}
                
                await entity_ref.update(entity_update)
                logger.info(f"Successfully updated ENTITIES with organisation details for entity_id: {entity_id}. ShortCode: {org_details.get('ShortCode')}")
                
                await _create_audit_log_entry(
                    db, "SYNC", "ORGANISATION_SYNC_SUCCESS", client_id, entity_id, "SUCCESS", 
                    {"shortCode": org_details.get("ShortCode"), "organisationID": org_details.get("OrganisationID"), "syncJobId": sync_job_id}
                )
            else:
                logger.warning(f"No organisation details returned for entity_id: {entity_id}")
                await _create_audit_log_entry(
                    db, "SYNC", "ORGANISATION_SYNC_WARNING", client_id, entity_id, "WARNING", 
                    {"error": "No organisation details returned", "syncJobId": sync_job_id}
                )
        except Exception as e_org:
            logger.error(f"Error syncing Organisation for entity_id {entity_id}: {e_org}", exc_info=True)
            await _create_audit_log_entry(
                db, "SYNC", "ORGANISATION_SYNC_FAILURE", client_id, entity_id, "FAILURE", 
                {"error": str(e_org), "syncJobId": sync_job_id}
            )

        # Contacts Sync (MODIFIED SECTION)
        if "Contacts" in requested_endpoints or not requested_endpoints:
            logger.info(f"Starting Contacts sync for entity_id: {entity_id}, client_id: {client_id}")
            try:
                full_sync = "Contacts" in force_full_sync_endpoints
                last_sync_timestamp_utc_str = None
                if not full_sync:
                    sync_ts_field = "_system_lastSyncTimestampUtc_Contacts"
                    last_sync_timestamp_utc_str = entity_settings.get(sync_ts_field)
                
                contacts_data = await xero_client.get_records("Contacts", if_modified_since=last_sync_timestamp_utc_str)
                saved_contacts_count = 0
                for contact in contacts_data:
                    contact_id_from_xero = contact.get("ContactID")
                    if not contact_id_from_xero:
                        logger.warning(f"Contact data missing ContactID for entity {entity_id}. Skipping: {contact}")
                        continue
                    
                    counterparty_doc = {
                        "counterparty_id": contact_id_from_xero, # Using Xero ID for our main ID field
                        "client_id": client_id,
                        "entity_id": entity_id,
                        "source_system": "XERO",
                        "source_system_id": contact_id_from_xero,
                        "name": contact.get("Name"),
                        "email_address": contact.get("EmailAddress"),
                        "phones": contact.get("Phones", []),
                        "addresses": contact.get("Addresses", []),
                        "is_supplier": contact.get("IsSupplier", False),
                        "is_customer": contact.get("IsCustomer", False),
                        "contact_persons": contact.get("ContactPersons", []),
                        "default_currency": contact.get("DefaultCurrency"),
                        "status": contact.get("ContactStatus"),
                        "raw_xero_data": contact,
                        "created_at": firestore.SERVER_TIMESTAMP,
                        "updated_at": firestore.SERVER_TIMESTAMP,
                        "source_updated_at_utc": contact.get("UpdatedDateUTC")
                    }
                    counterparty_doc = {k: v for k, v in counterparty_doc.items() if v is not None}

                    counterparty_ref = db.collection("COUNTERPARTIES").document(contact_id_from_xero)
                    await counterparty_ref.set(counterparty_doc, merge=True)
                    saved_contacts_count += 1
                
                logger.info(f"Successfully synced and saved {saved_contacts_count} Contacts to COUNTERPARTIES for entity_id: {entity_id}")
                await _update_firestore_sync_timestamp(
                    db, entity_id, "Contacts", datetime.now(timezone.utc).isoformat(), "Contacts sync successful"
                )
                await _create_audit_log_entry(
                    db, "SYNC", "CONTACTS_SYNC_SUCCESS", client_id, entity_id, "SUCCESS", 
                    {"count": saved_contacts_count, "syncJobId": sync_job_id}
                )
            except Exception as e_contacts:
                logger.error(f"Error syncing Contacts for entity_id {entity_id}: {e_contacts}", exc_info=True)
                await _create_audit_log_entry(
                    db, "SYNC", "CONTACTS_SYNC_FAILURE", client_id, entity_id, "FAILURE", 
                    {"error": str(e_contacts), "syncJobId": sync_job_id}
                )

        # Accounts Sync (NEW SECTION)
        if "Accounts" in requested_endpoints or not requested_endpoints:
            logger.info(f"Starting Accounts sync for entity_id: {entity_id}, client_id: {client_id}")
            try:
                full_sync = "Accounts" in force_full_sync_endpoints
                last_sync_timestamp_utc_str = None
                if not full_sync:
                    sync_ts_field = "_system_lastSyncTimestampUtc_Accounts"
                    last_sync_timestamp_utc_str = entity_settings.get(sync_ts_field)
                
                accounts_data = await xero_client.get_records("Accounts", if_modified_since=last_sync_timestamp_utc_str)
                saved_accounts_count = 0
                
                # Use batch write for better performance
                batch = db.batch()
                batch_count = 0
                
                for account in accounts_data:
                    account_id_from_xero = account.get("AccountID")
                    if not account_id_from_xero:
                        logger.warning(f"Account data missing AccountID for entity {entity_id}. Skipping: {account}")
                        continue
                    
                    account_doc_id = f"{entity_id}_{account_id_from_xero}"
                    account_doc = {
                        "entity_id": entity_id,
                        "client_id": client_id,
                        "account_id": account_id_from_xero,
                        "code": account.get("Code"),
                        "name": account.get("Name"),
                        "type": account.get("Type"),
                        "class": account.get("Class"),
                        "status": account.get("Status"),
                        "description": account.get("Description"),
                        "bank_account_number": account.get("BankAccountNumber"),
                        "bank_account_type": account.get("BankAccountType"),
                        "currency_code": account.get("CurrencyCode"),
                        "tax_type": account.get("TaxType"),
                        "enable_payments_to_account": account.get("EnablePaymentsToAccount"),
                        "show_in_expense_claims": account.get("ShowInExpenseClaims"),
                        "source_system": "XERO",
                        "raw_xero_data": account,
                        "created_at": firestore.SERVER_TIMESTAMP,
                        "updated_at": firestore.SERVER_TIMESTAMP,
                        "source_updated_at_utc": account.get("UpdatedDateUTC")
                    }
                    account_doc = {k: v for k, v in account_doc.items() if v is not None}

                    account_ref = db.collection("CHART_OF_ACCOUNTS").document(account_doc_id)
                    batch.set(account_ref, account_doc, merge=True)
                    saved_accounts_count += 1
                    batch_count += 1
                    
                    # Commit batch every 500 documents (Firestore limit)
                    if batch_count >= 500:
                        await batch.commit()
                        batch = db.batch()
                        batch_count = 0
                
                # Commit remaining documents
                if batch_count > 0:
                    await batch.commit()
                
                logger.info(f"Successfully synced and saved {saved_accounts_count} Accounts to CHART_OF_ACCOUNTS for entity_id: {entity_id}")
                await _update_firestore_sync_timestamp(
                    db, entity_id, "Accounts", datetime.now(timezone.utc).isoformat(), "Accounts sync successful"
                )
                await _create_audit_log_entry(
                    db, "SYNC", "ACCOUNTS_SYNC_SUCCESS", client_id, entity_id, "SUCCESS", 
                    {"count": saved_accounts_count, "syncJobId": sync_job_id}
                )
            except Exception as e_accounts:
                logger.error(f"Error syncing Accounts for entity_id {entity_id}: {e_accounts}", exc_info=True)
                await _create_audit_log_entry(
                    db, "SYNC", "ACCOUNTS_SYNC_FAILURE", client_id, entity_id, "FAILURE", 
                    {"error": str(e_accounts), "syncJobId": sync_job_id}
                )

        # Invoices/Transactions Sync (NEW SECTION - MISSING IMPLEMENTATION)
        if "Invoices" in requested_endpoints or not requested_endpoints:
            logger.info(f"Starting Invoices/Transactions sync for entity_id: {entity_id}, client_id: {client_id}")
            try:
                full_sync = "Invoices" in force_full_sync_endpoints
                last_sync_timestamp_utc_str = None
                transaction_sync_start_date = message_data.get("transactionSyncStartDate")
                
                if not full_sync and not transaction_sync_start_date:
                    sync_ts_field = "_system_lastSyncTimestampUtc_Invoices"
                    last_sync_timestamp_utc_str = entity_settings.get(sync_ts_field)
                
                # Build where filter for ACCREC invoices (customer invoices)
                where_filter = 'Type=="ACCREC"'
                if transaction_sync_start_date:
                    # Parse the date and format for Xero DateTime constructor
                    try:
                        date_obj = datetime.strptime(transaction_sync_start_date, '%Y-%m-%d').date()
                        where_filter += f' AND Date>=DateTime({date_obj.year},{date_obj.month:02d},{date_obj.day:02d},00,00,00)'
                    except ValueError:
                        logger.warning(f"Invalid transactionSyncStartDate format: {transaction_sync_start_date}, skipping date filter")
                
                logger.info(f"Fetching ACCREC invoices with filter: {where_filter}")
                invoices_data = await xero_client.get_records(
                    "Invoices", 
                    where_filter=where_filter,
                    if_modified_since=last_sync_timestamp_utc_str if not transaction_sync_start_date else None
                )
                
                saved_invoices_count = 0
                
                # Initialize financial document adapter
                try:
                    from .financial_doc_adapter import FinancialDocAdapter
                except ImportError:
                    # Fallback for local testing
                    import sys
                    import os
                    sys.path.append(os.path.dirname(__file__))
                    from financial_doc_adapter import FinancialDocAdapter
                doc_adapter = FinancialDocAdapter(entity_id=entity_id)
                
                # Process invoices in batches
                batch = db.batch()
                batch_count = 0
                
                for invoice in invoices_data:
                    invoice_id = invoice.get("InvoiceID")
                    if not invoice_id:
                        logger.warning(f"Invoice data missing InvoiceID for entity {entity_id}. Skipping: {invoice}")
                        continue
                    
                    # Transform invoice using adapter
                    try:
                        standardized_invoice = await doc_adapter._transform_invoice(invoice)
                    except Exception as e_transform:
                        logger.error(f"Error transforming invoice {invoice_id}: {e_transform}")
                        continue
                    
                    # Create transaction document for customer invoice
                    transaction_doc = {
                        "transaction_id": invoice_id,
                        "entity_id": entity_id,
                        "client_id": client_id,
                        "source_system": "XERO",
                        "source_system_id": invoice_id,
                        "document_type": "INVOICE",
                        "transaction_type": "ACCREC",
                        "document_number": invoice.get("InvoiceNumber"),
                        "reference": invoice.get("Reference"),
                        "status": invoice.get("Status"),
                        "date_issued": standardized_invoice.get("date_issued"),
                        "date_due": standardized_invoice.get("date_due"),
                        "currency_code": invoice.get("CurrencyCode"),
                        "subtotal": invoice.get("SubTotal", 0),
                        "tax_total": invoice.get("TotalTax", 0),
                        "total_amount": invoice.get("Total", 0),
                        "amount_paid": invoice.get("AmountPaid", 0),
                        "amount_due": invoice.get("AmountDue", 0),
                        "contact_id": invoice.get("Contact", {}).get("ContactID"),
                        "contact_name": invoice.get("Contact", {}).get("Name"),
                        "line_items": invoice.get("LineItems", []),
                        "raw_xero_data": invoice,
                        "standardized_data": standardized_invoice,
                        "created_at": firestore.SERVER_TIMESTAMP,
                        "updated_at": firestore.SERVER_TIMESTAMP,
                        "source_updated_at_utc": invoice.get("UpdatedDateUTC"),
                        "has_amortization_schedules": False,
                        "prepayment_analysis": {
                            "gl_based_analysis_completed": True,  # Customer invoices don't typically have prepayments
                            "llm_based_analysis_completed": True,
                            "has_prepayment_line_items": False,
                            "prepayment_line_items": []
                        }
                    }
                    
                    # Remove None values
                    transaction_doc = {k: v for k, v in transaction_doc.items() if v is not None}
                    
                    # Save to TRANSACTIONS collection
                    transaction_ref = db.collection("TRANSACTIONS").document(invoice_id)
                    batch.set(transaction_ref, transaction_doc, merge=True)
                    saved_invoices_count += 1
                    batch_count += 1
                    
                    # Commit batch every 100 documents for transactions (smaller batches due to size)
                    if batch_count >= 100:
                        await batch.commit()
                        batch = db.batch()
                        batch_count = 0
                
                # Commit remaining documents
                if batch_count > 0:
                    await batch.commit()
                
                logger.info(f"Successfully synced {saved_invoices_count} customer invoices for entity_id: {entity_id}")
                await _update_firestore_sync_timestamp(
                    db, entity_id, "Invoices", datetime.now(timezone.utc).isoformat(), "Customer invoices sync successful"
                )
                await _create_audit_log_entry(
                    db, "SYNC", "INVOICES_SYNC_SUCCESS", client_id, entity_id, "SUCCESS", 
                    {"invoices_count": saved_invoices_count, "syncJobId": sync_job_id}
                )
                
            except Exception as e_invoices:
                logger.error(f"Error syncing customer invoices for entity_id {entity_id}: {e_invoices}", exc_info=True)
                await _create_audit_log_entry(
                    db, "SYNC", "INVOICES_SYNC_FAILURE", client_id, entity_id, "FAILURE", 
                    {"error": str(e_invoices), "syncJobId": sync_job_id}
                )

        # Bills Sync (NEW SECTION - ACCPAY bills with prepayment processing)
        if "Bills" in requested_endpoints or not requested_endpoints:
            logger.info(f"Starting Bills sync for entity_id: {entity_id}, client_id: {client_id}")
            try: # Main try for the entire Bills sync operation
                full_sync = "Bills" in force_full_sync_endpoints
                last_sync_timestamp_utc_str = None
                transaction_sync_start_date = message_data.get("transactionSyncStartDate")
                
                if not full_sync and not transaction_sync_start_date:
                    sync_ts_field = "_system_lastSyncTimestampUtc_Bills"
                    last_sync_timestamp_utc_str = entity_settings.get(sync_ts_field)
                
                where_filter = 'Type=="ACCPAY"'
                if transaction_sync_start_date:
                    try:
                        date_obj = datetime.strptime(transaction_sync_start_date, '%Y-%m-%d').date()
                        where_filter += f' AND Date>=DateTime({date_obj.year},{date_obj.month:02d},{date_obj.day:02d},00,00,00)'
                    except ValueError:
                        logger.warning(f"Invalid transactionSyncStartDate format: {transaction_sync_start_date}, skipping date filter")
                
                logger.info(f"Fetching ACCPAY bills with filter: {where_filter}")
                bills_data = await xero_client.get_records(
                    "Invoices", 
                    where_filter=where_filter,
                    if_modified_since=last_sync_timestamp_utc_str if not transaction_sync_start_date else None
                )
                
                saved_bills_count = 0
                processed_prepayments_count = 0
                
                try:
                    from .financial_doc_adapter import FinancialDocAdapter
                except ImportError:
                    import sys
                    import os
                    sys.path.append(os.path.dirname(__file__))
                    from financial_doc_adapter import FinancialDocAdapter
                doc_adapter = FinancialDocAdapter(entity_id=entity_id)
                
                batch = db.batch()
                batch_count = 0
                
                for bill in bills_data:
                    bill_id = bill.get("InvoiceID")
                    if not bill_id:
                        logger.warning(f"Bill data missing InvoiceID for entity {entity_id}. Skipping: {bill}")
                        continue
                    
                    try:
                        standardized_bill = await doc_adapter._transform_invoice(bill)
                    except Exception as e_transform:
                        logger.error(f"Error transforming bill {bill_id}: {e_transform}")
                        continue
                    
                    # Create a clean, normalized transaction document structure
                    transaction_doc = {
                        # Core identifiers
                        "transaction_id": bill_id,
                        "entity_id": entity_id,
                        "client_id": client_id,
                        "source_system": "XERO",
                        "source_system_id": bill_id,

                        # Document classification
                        "document_type": "BILL",
                        "transaction_type": "ACCPAY",
                        "document_number": bill.get("InvoiceNumber"),
                        "reference": bill.get("Reference"),
                        "status": bill.get("Status"),

                        # Dates (use standardized format)
                        "date_issued": standardized_bill.get("date_issued"),
                        "date_due": standardized_bill.get("date_due"),
                        "source_updated_at_utc": bill.get("UpdatedDateUTC"),

                        # Financial amounts (use Xero values as source of truth)
                        "currency_code": bill.get("CurrencyCode"),
                        "subtotal": bill.get("SubTotal", 0),
                        "tax_total": bill.get("TotalTax", 0),
                        "total_amount": bill.get("Total", 0),
                        "amount_paid": bill.get("AmountPaid", 0),
                        "amount_due": bill.get("AmountDue", 0),

                        # Contact information
                        "contact_id": bill.get("Contact", {}).get("ContactID"),
                        "contact_name": bill.get("Contact", {}).get("Name"),

                        # Line items (essential for prepayment analysis)
                        "line_items": bill.get("LineItems", []),

                        # System fields
                        "has_amortization_schedules": False,
                        "prepayment_analysis": {
                            "gl_based_analysis_completed": False,
                            "llm_based_analysis_completed": False,
                            "has_prepayment_line_items": False,
                            "prepayment_line_items": [],
                            "recommended_action": "pending_analysis"
                        },

                        # Metadata (store Xero URL from standardized data)
                        "xero_url": standardized_bill.get("metadata", {}).get("xero_url"),

                        # Timestamps
                        "created_at": firestore.SERVER_TIMESTAMP,
                        "updated_at": firestore.SERVER_TIMESTAMP
                    }
                    transaction_doc = {k: v for k, v in transaction_doc.items() if v is not None}
                    
                    transaction_ref = db.collection("TRANSACTIONS").document(bill_id)
                    batch.set(transaction_ref, transaction_doc, merge=True)
                    saved_bills_count += 1
                    batch_count += 1
                    
                    if batch_count >= 100:
                        await batch.commit()
                        batch = db.batch()
                        batch_count = 0
                    
                    try:
                        prepayment_asset_codes = entity_settings.get("prepayment_asset_account_codes", [])
                        gl_prepayment_lines = [li for li in bill.get("LineItems", []) if li.get("AccountCode") in prepayment_asset_codes]
                        processed_attachments = []
                        if entity_settings.get("enable_llm_prepayment_detection", True):
                            processed_attachments = await _fetch_and_process_attachments(xero_client, bill_id, bill, entity_settings, entity_id, db, f"bill_{bill_id}")
                        combined_analysis = await _perform_combined_prepayment_analysis(bill, gl_prepayment_lines, processed_attachments, entity_settings)
                        
                        if combined_analysis["recommended_action"] == "create_amortization_schedule":
                            for prepayment_line in gl_prepayment_lines:
                                try:
                                    best_period = combined_analysis.get("best_service_period")
                                    service_start_date, service_end_date = (best_period.get("start_date"), best_period.get("end_date")) if best_period and best_period.get("confidence") in ["high", "medium"] else (None, None)
                                    if service_start_date and service_end_date: logger.info(f"Using LLM-extracted service period for bill {bill_id}: {service_start_date} to {service_end_date}")
                                    schedule_id = await _generate_and_save_amortization_schedule(db, bill_id, prepayment_line, entity_settings.get("_system_masterPeriodStartDate"), entity_settings.get("_system_masterPeriodEndDate"), entity_settings, client_id, entity_id, bill, service_start_date, service_end_date)
                                    if schedule_id: processed_prepayments_count += 1; logger.info(f"Generated amortization schedule {schedule_id} for bill {bill_id}")
                                except Exception as e_schedule:
                                    logger.error(f"Error generating amortization schedule for bill {bill_id}: {e_schedule}")
                        
                        update_data = {"prepayment_analysis": combined_analysis, "has_amortization_schedules": combined_analysis["recommended_action"] == "create_amortization_schedule", "attachments_processed": len(processed_attachments), "llm_processing_completed": combined_analysis["llm_based_analysis_completed"]}

                        # Use set with merge=True to handle cases where the document might not exist yet
                        try:
                            # Check if document exists first
                            doc_check = await transaction_ref.get()
                            if doc_check.exists:
                                await transaction_ref.update(update_data)
                                logger.debug(f"Successfully updated TRANSACTIONS document {bill_id} with prepayment analysis")
                            else:
                                # Document doesn't exist, use set with merge=True
                                logger.warning(f"TRANSACTIONS document {bill_id} does not exist yet. Using set with merge=True for prepayment analysis update.")
                                update_data.update({
                                    "transaction_id": bill_id,
                                    "entity_id": entity_id,
                                    "client_id": client_id,
                                    "updated_at": firestore.SERVER_TIMESTAMP
                                })
                                await transaction_ref.set(update_data, merge=True)
                                logger.debug(f"Successfully created/merged TRANSACTIONS document {bill_id} with prepayment analysis")
                        except Exception as e_update:
                            logger.error(f"Failed to update TRANSACTIONS document {bill_id} with prepayment analysis: {e_update}", exc_info=True)
                            # Store the error in the prepayment analysis for debugging
                            update_data["prepayment_analysis"]["error"] = str(e_update)
                            # Try one more time with set and merge=True as fallback
                            try:
                                update_data.update({
                                    "transaction_id": bill_id,
                                    "entity_id": entity_id,
                                    "client_id": client_id,
                                    "updated_at": firestore.SERVER_TIMESTAMP
                                })
                                await transaction_ref.set(update_data, merge=True)
                                logger.info(f"Fallback: Successfully created TRANSACTIONS document {bill_id} with prepayment analysis error")
                            except Exception as e_fallback:
                                logger.error(f"Fallback also failed for TRANSACTIONS document {bill_id}: {e_fallback}", exc_info=True)
                    except Exception as e_prepayment:
                        current_bill_id = bill.get("InvoiceID", "N/A") if 'bill' in locals() and isinstance(bill, dict) else "N/A"
                        logger.error(f"Error processing prepayments for a BILL (ID: {current_bill_id}): {e_prepayment}")
                        if batch_count > 0:
                            try: logger.info(f"Committing batch before fallback update for bill {current_bill_id}"); await batch.commit()
                            except Exception as e_bc: logger.error(f"Error committing batch before fallback for bill {current_bill_id}: {e_bc}")
                            finally: batch = db.batch(); batch_count = 0
                        try:
                            fallback_data = {"prepayment_analysis.gl_based_analysis_completed": True, "prepayment_analysis.llm_based_analysis_completed": False, "prepayment_analysis.has_prepayment_line_items": len(gl_prepayment_lines) > 0, "prepayment_analysis.error": str(e_prepayment)}
                            ref_for_fallback = db.collection("TRANSACTIONS").document(current_bill_id)

                            # Check if document exists before attempting update
                            fallback_doc_check = await ref_for_fallback.get()
                            if fallback_doc_check.exists:
                                await ref_for_fallback.update(fallback_data)
                                logger.info(f"Applied fallback update for existing bill {current_bill_id}")
                            else:
                                # Document doesn't exist, create it with fallback data using set with merge=True
                                logger.warning(f"TRANSACTIONS document {current_bill_id} does not exist for fallback. Creating with minimal data.")
                                fallback_create_data = {
                                    "transaction_id": current_bill_id,
                                    "entity_id": entity_id,
                                    "client_id": client_id,
                                    "prepayment_analysis": {
                                        "gl_based_analysis_completed": True,
                                        "llm_based_analysis_completed": False,
                                        "has_prepayment_line_items": len(gl_prepayment_lines) > 0,
                                        "error": str(e_prepayment)
                                    },
                                    "created_at": firestore.SERVER_TIMESTAMP,
                                    "updated_at": firestore.SERVER_TIMESTAMP
                                }
                                await ref_for_fallback.set(fallback_create_data, merge=True)
                                logger.info(f"Applied fallback creation for bill {current_bill_id}")
                        except Exception as e_fb:
                            logger.error(f"Failed fallback for bill {current_bill_id}: {e_fb}", exc_info=True)
                
                if batch_count > 0:
                    try: logger.info(f"Committing remaining {batch_count} bills at end of Bills sync."); await batch.commit()
                    except Exception as e_final_b: logger.error(f"Error committing final batch in Bills sync: {e_final_b}")
                    finally: batch = db.batch(); batch_count = 0
                
                logger.info(f"Successfully synced {saved_bills_count} Bills and processed {processed_prepayments_count} prepayments for entity_id: {entity_id}")
                await _update_firestore_sync_timestamp(db, entity_id, "Bills", datetime.now(timezone.utc).isoformat(), "Bills sync successful")
                await _create_audit_log_entry(db, "SYNC", "BILLS_SYNC_SUCCESS", client_id, entity_id, "SUCCESS", {"bills_count": saved_bills_count, "prepayments_count": processed_prepayments_count, "syncJobId": sync_job_id})

            except Exception as e_bills:
                logger.error(f"Error syncing Bills for entity_id {entity_id}: {e_bills}", exc_info=True)
                await _create_audit_log_entry(db, "SYNC", "BILLS_SYNC_FAILURE", client_id, entity_id, "FAILURE", {"error": str(e_bills), "syncJobId": sync_job_id})
        # END OF BILLS SYNC SECTION

        # Proposed Journals Processing (Calls to helpers remain the same, but helpers are modified)
        if "ProposedJournals" in requested_endpoints or not requested_endpoints:
            await _generate_proposed_journals_for_due_entries(
                db=db, client_id=client_id, entity_id=entity_id, entity_settings=entity_settings, target_date=target_date
            )
            if entity_settings.get("_system_autoPostProposedJournalsToXero", False):
                await _post_proposed_journals_to_xero(
                    db=db, client_id=client_id, entity_id=entity_id, entity_settings=entity_settings, xero_client_for_posting=xero_client
                )
        
        await _create_audit_log_entry(
            db, "SYNC", "XERO_SYNC_JOB_COMPLETED", client_id, entity_id, "SUCCESS", 
            {"syncJobId": sync_job_id, "processedEndpoints": requested_endpoints}
        )
        
        # Update entity status back to "active" after successful sync completion
        try:
            entity_ref = db.collection("ENTITIES").document(entity_id)
            await entity_ref.update({
                "status": "active",
                "updated_at": firestore.SERVER_TIMESTAMP
            })
            logger.info(f"Updated entity {entity_id} status back to 'active' after successful sync completion")
        except Exception as e_status_update:
            logger.warning(f"Failed to update entity {entity_id} status back to 'active': {e_status_update}")
        
        logger.info(f"Xero sync job completed successfully for entity_id: {entity_id}")

    except Exception as e_main:
        logger.error(f"Unhandled error in Xero sync consumer for entity_id {entity_id}: {e_main}", exc_info=True)
        # Only create audit log if db is available
        if db:
            await _create_audit_log_entry(
                db, "SYNC", "XERO_SYNC_JOB_CRITICAL_FAILURE", client_id, entity_id, "FAILURE", 
                {"error": str(e_main), "syncJobId": sync_job_id, "message_data": message_data if 'message_data' in locals() else event.get("data")}
            )
            
            # Update entity status back to "active" even on failure to prevent stuck "syncing" state
            try:
                entity_ref = db.collection("ENTITIES").document(entity_id)
                await entity_ref.update({
                    "status": "active",
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Updated entity {entity_id} status back to 'active' after sync failure")
            except Exception as e_status_update:
                logger.warning(f"Failed to update entity {entity_id} status back to 'active' after failure: {e_status_update}")
        else:
            logger.warning("Cannot create audit log entry for critical failure: Firestore client not initialized")
    finally:
        if db: 
            try:
                # Check if close() method exists and is awaitable
                if hasattr(db, 'close') and callable(getattr(db, 'close')):
                    close_result = db.close()
                    if close_result is not None:
                        await close_result
                    logger.info("Firestore client closed successfully.")
                else:
                    logger.info("Firestore client does not have close method.")
            except Exception as close_error:
                logger.error(f"Error closing Firestore client: {close_error}")
        else:
            logger.info("Firestore client was None, no need to close.")


async def _generate_and_save_amortization_schedule(
    db: firestore.AsyncClient,
    invoice_id: str, 
    line_item_data: Dict[str, Any],
    master_period_start_date: datetime.date,
    master_period_end_date: datetime.date,
    entity_settings: Dict[str, Any],
    client_id: Optional[str],
    entity_id: str,
    parent_invoice_data: Dict[str, Any],
    llm_service_start_date: Optional[str] = None,
    llm_service_end_date: Optional[str] = None
) -> Optional[str]:
    line_item_id = line_item_data.get("LineItemID")
    logger.info(
        f"Attempting to generate amortization schedule for Invoice: {invoice_id}, LineItem: {line_item_id}"
    )

    if not client_id:
        logger.warning(
            f"Cannot generate schedule for Invoice {invoice_id}, Line {line_item_id}: missing clientId in entity_settings."
        )
        return None

    prepayment_asset_codes = entity_settings.get("prepayment_asset_account_codes", [])
    amortization_account_code = (
        prepayment_asset_codes[0] if prepayment_asset_codes else None
    )
    if not amortization_account_code:
        logger.warning(
            f"Cannot generate schedule for Invoice {invoice_id}, Line {line_item_id}: No prepayment_asset_account_codes defined in entity settings for {entity_id}."
        )
        return None

    # Determine the initial expense account code from the line item
    original_line_item_account_code = line_item_data.get("AccountCode")
    resolved_expense_account_code = original_line_item_account_code # Start with this

    if not resolved_expense_account_code:
        logger.warning(
            f"Cannot generate schedule for Invoice {invoice_id}, Line {line_item_id}: Missing AccountCode on line item."
        )
        return None

    # Check if the line item is coded to a prepayment asset account
    if original_line_item_account_code in prepayment_asset_codes:
        logger.info(
            f"Invoice {invoice_id}, Line {line_item_id}: Line item AccountCode {original_line_item_account_code} is a prepayment asset account. Attempting to use vendor default expense account."
        )
        contact_data = parent_invoice_data.get("Contact") # Contact field in Xero Invoice is a summary
        if contact_data and isinstance(contact_data, dict):
            contact_id_from_invoice = contact_data.get("ContactID") # This is the Xero ContactID
            if contact_id_from_invoice:
                try:
                    # MODIFIED: Read from top-level COUNTERPARTIES using Xero ContactID as document ID
                    contact_doc_ref = db.collection("COUNTERPARTIES").document(contact_id_from_invoice)
                    contact_doc = await contact_doc_ref.get()
                    if contact_doc.exists:
                        contact_firestore_data = contact_doc.to_dict()
                        # Ensure the fetched counterparty belongs to the same entity_id and client_id for integrity
                        if contact_firestore_data.get("entity_id") != entity_id or contact_firestore_data.get("client_id") != client_id:
                            logger.error(f"Invoice {invoice_id}, Line {line_item_id}: Fetched Counterparty {contact_id_from_invoice} does not match expected entity_id/client_id. Data integrity issue? Skipping schedule.")
                            return None
                        vendor_default_expense_code = contact_firestore_data.get("_system_defaultAmortizationExpenseAccountCode")
                        if vendor_default_expense_code:
                            logger.info(f"Invoice {invoice_id}, Line {line_item_id}: Found vendor default expense account {vendor_default_expense_code} for Contact {contact_id_from_invoice}.")
                            # CRITICAL: Ensure the default expense account itself is not a prepayment asset account
                            if vendor_default_expense_code in prepayment_asset_codes:
                                logger.error(f"Invoice {invoice_id}, Line {line_item_id}: Vendor default expense account {vendor_default_expense_code} for Contact {contact_id_from_invoice} is itself a prepayment asset account. Misconfiguration. Cannot generate schedule.")
                                return None # Misconfiguration
                            resolved_expense_account_code = vendor_default_expense_code
                        else:
                            logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Contact {contact_id_from_invoice} exists but has no '_system_defaultAmortizationExpenseAccountCode' defined. Cannot generate schedule for this GL-coded prepayment line.")
                            return None
                    else:
                        logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Counterparty document for ID {contact_id_from_invoice} not found in COUNTERPARTIES. Cannot determine vendor default expense account.")
                        return None
                except Exception as e_contact_fetch:
                    logger.error(f"Invoice {invoice_id}, Line {line_item_id}: Error fetching counterparty {contact_id_from_invoice} from COUNTERPARTIES: {e_contact_fetch}", exc_info=True)
                    return None
            else:
                logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: ContactID not found on parent invoice. Cannot determine vendor default expense account.")
                return None
        else:
            logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Contact data not found or invalid on parent invoice. Cannot determine vendor default expense account.")
            return None
    
    # Final check on the resolved expense account code (could be original or from vendor default)
    if not resolved_expense_account_code: # Should be caught by earlier checks, but as a safeguard
        logger.error(f"Invoice {invoice_id}, Line {line_item_id}: Resolved expense_account_code is missing before proceeding. This should not happen.")
        return None
    
    # The original check still applies if, after resolving, it's still a prepayment account (e.g. if a non-GL coded line somehow got here, which is unlikely)
    # OR if the original line was NOT GL-coded to an asset, this check runs on its original AccountCode.
    if resolved_expense_account_code in prepayment_asset_codes:
        logger.warning(
            f"Invoice {invoice_id}, Line {line_item_id}: Final resolved AccountCode {resolved_expense_account_code} is a prepayment asset account. Cannot generate schedule. (This might occur if a non-GL line was passed or default was misconfigured)"
        )
        return None

    # Assign to the variable name used by the rest of the function
    expense_account_code = resolved_expense_account_code

    total_amount_to_amortize = line_item_data.get("LineAmount")
    if total_amount_to_amortize is None or total_amount_to_amortize == 0:
        logger.info(
            f"Invoice {invoice_id}, Line {line_item_id}: Amount to amortize is zero or None. No schedule generated."
        )
        return None

    # Determine service period: Use LLM-extracted dates if available, otherwise fall back to master period
    start_date = master_period_start_date
    end_date = master_period_end_date
    service_period_source = "master_period"
    
    if llm_service_start_date and llm_service_end_date:
        try:
            llm_start = _parse_date_string_to_date(llm_service_start_date)
            llm_end = _parse_date_string_to_date(llm_service_end_date)
            
            if llm_start and llm_end and llm_start <= llm_end:
                start_date = llm_start
                end_date = llm_end
                service_period_source = "llm_extracted"
                logger.info(f"Invoice {invoice_id}, Line {line_item_id}: Using LLM-extracted service period: {llm_service_start_date} to {llm_service_end_date}")
            else:
                logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Invalid LLM service dates ({llm_service_start_date} to {llm_service_end_date}), falling back to master period")
        except Exception as e_date_parse:
            logger.warning(f"Invoice {invoice_id}, Line {line_item_id}: Error parsing LLM service dates: {e_date_parse}, falling back to master period")
    
    line_item_description = line_item_data.get("Description", "N/A")

    # --- NEW PREPAYMENT DURATION CHECK using > 32 days ---
    if start_date and end_date:  # Ensure dates are valid
        num_days_in_period = (end_date - start_date).days + 1  # Inclusive day count
        if num_days_in_period <= 32:
            logger.info(
                f"Invoice {invoice_id}, Line {line_item_id}: Period length ({num_days_in_period} days) is <= 32 days. Does not qualify as prepayment. No schedule generated."
            )
            return None
        else:
            logger.info(
                f"Invoice {invoice_id}, Line {line_item_id}: Period length ({num_days_in_period} days) qualifies as prepayment. Proceeding with schedule generation using {service_period_source} dates."
            )
    else:  # Should have been caught earlier by main loop's check, but good to be safe
        logger.warning(
            f"Invoice {invoice_id}, Line {line_item_id}: Missing start or end date for duration check. Skipping schedule."
        )
        return None
    # --- END NEW PREPAYMENT DURATION CHECK ---

    # Now calculate num_periods for monthly distribution, knowing it IS a prepayment
    monthly_entries = []
    current_month_start = datetime(start_date.year, start_date.month, 1).date()
    num_periods = 0
    temp_date = start_date
    unique_months = set()
    while temp_date <= end_date:
        unique_months.add((temp_date.year, temp_date.month))
        if temp_date.month == 12:
            temp_date = datetime(temp_date.year + 1, 1, 1).date()
        else:
            temp_date = datetime(temp_date.year, temp_date.month + 1, 1).date()
    num_periods = len(unique_months)

    if num_periods == 0:
        logger.warning(
            f"Calculated zero periods for amortization between {start_date} and {end_date} for Invoice {invoice_id}, Line {line_item_id}. Skipping schedule."
        )
        return None

    amount_per_period = round(total_amount_to_amortize / num_periods, 2)
    running_total = 0

    for i in range(num_periods):
        period_date = current_month_start
        entry_amount = amount_per_period
        if i == num_periods - 1:
            entry_amount = round(total_amount_to_amortize - running_total, 2)

        monthly_entries.append(
            {
                "monthDate": datetime(
                    period_date.year,
                    period_date.month,
                    period_date.day,
                    tzinfo=timezone.utc,
                ),
                "amount": entry_amount,
                "status": "proposed",
                "postedJournalId": None,
                "postedJournalLineId": None,
                "matchConfidence": None,
                "lastActionByUserId": None,
                "lastActionTimestamp": None,
                "postingError": None,
            }
        )
        running_total += entry_amount

        if current_month_start.month == 12:
            current_month_start = datetime(current_month_start.year + 1, 1, 1).date()
        else:
            current_month_start = datetime(
                current_month_start.year, current_month_start.month + 1, 1
            ).date()

    schedule_id_val = str(uuid.uuid4()) # Renamed from schedule_id to avoid conflict with field name
    schedule_data = {
        "schedule_id": schedule_id_val, # Use the generated UUID as the primary ID for the schedule
        "transaction_id": invoice_id,
        "line_item_id": line_item_id,
        "entity_id": entity_id, # Ensure this is our standardized entity_id
        "client_id": client_id, # Ensure this is our standardized client_id
        "status": "proposed",
        "originalAmount": total_amount_to_amortize,
        "amortizationStartDate": datetime(
            start_date.year, start_date.month, start_date.day, tzinfo=timezone.utc
        ),
        "amortizationEndDate": datetime(
            end_date.year, end_date.month, end_date.day, tzinfo=timezone.utc
        ),
        "numberOfPeriods": num_periods,
        "periodType": "monthly",
        "amortizationAccountCode": amortization_account_code,
        "expenseAccountCode": expense_account_code,
        "description": f"Amortization for: {line_item_description[:100]}",
        "monthlyEntries": monthly_entries,
        "created_at": firestore.SERVER_TIMESTAMP,
        "updated_at": firestore.SERVER_TIMESTAMP,
    }
    schedule_data = {k: v for k, v in schedule_data.items() if v is not None} # Clean None values

    try:
        schedule_doc_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id_val)
        await schedule_doc_ref.set(schedule_data)
        logger.info(
            f"Firestore 'set' command completed for schedule {schedule_id_val} in top-level AMORTIZATION_SCHEDULES. Attempting to verify..."
        )

        # TRY TO READ IT BACK IMMEDIATELY
        verify_doc = await schedule_doc_ref.get()
        if verify_doc.exists:
            logger.info(
                f"VERIFIED: Successfully saved and read back AMORTIZATION_SCHEDULE {schedule_id_val} from top-level collection for Invoice {invoice_id}, Line {line_item_id}."
            )
        else:
            logger.error(
                f"VERIFICATION FAILED: AMORTIZATION_SCHEDULE {schedule_id_val} NOT FOUND after 'set' command to top-level collection for Invoice {invoice_id}, Line {line_item_id}."
            )
            return None  # Ensure we don't return a schedule_id if verification fails

        # Update parent transaction (this part assumes TRANSACTIONS is top-level)
        # Use set with merge=True to handle cases where the document might not exist yet
        parent_transaction_ref = db.collection("TRANSACTIONS").document(invoice_id)
        try:
            # First, check if the document exists
            parent_doc = await parent_transaction_ref.get()
            if parent_doc.exists:
                # Document exists, use update
                await parent_transaction_ref.update({
                    "_system_amortizationScheduleIDs": firestore.ArrayUnion([schedule_id_val]),
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                logger.info(f"Successfully updated existing TRANSACTIONS document {invoice_id} with amortization schedule {schedule_id_val}")
            else:
                # Document doesn't exist, use set with merge=True to create it with minimal data
                logger.warning(f"TRANSACTIONS document {invoice_id} does not exist yet. Creating with amortization schedule reference.")
                await parent_transaction_ref.set({
                    "transaction_id": invoice_id,
                    "entity_id": entity_id,
                    "client_id": client_id,
                    "_system_amortizationScheduleIDs": [schedule_id_val],
                    "created_at": firestore.SERVER_TIMESTAMP,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }, merge=True)
                logger.info(f"Successfully created TRANSACTIONS document {invoice_id} with amortization schedule {schedule_id_val}")
        except Exception as e_transaction_update:
            logger.error(f"Failed to update/create TRANSACTIONS document {invoice_id} with amortization schedule {schedule_id_val}: {e_transaction_update}", exc_info=True)
            # Don't fail the entire schedule creation if transaction update fails
            # The schedule is still valid and can be linked later
            logger.warning(f"Continuing with schedule creation despite transaction update failure for {invoice_id}")

        return schedule_id_val
    except Exception as e_fs_save_sched:
        logger.error(
            f"Failed to save or verify AMORTIZATION_SCHEDULE {schedule_id_val} at top-level collection for Invoice {invoice_id}, Line {line_item_id}: {e_fs_save_sched}",
            exc_info=True,
        )
        return None


async def _generate_proposed_journals_for_due_entries(
    db: firestore.AsyncClient,
    client_id: Optional[str],
    entity_id: str,
    entity_settings: Dict[str, Any],
    target_date: Optional[date] = None 
):
    logger.info(f"Starting proposed journal generation for entity {entity_id}, tenant {client_id} up to {target_date if target_date else 'today'}.")

    if not client_id:
        logger.error(f"Entity {entity_id}: Missing clientId in entity_settings. Cannot generate proposed journals.")
        return

    if not target_date:
        target_date = datetime.now(timezone.utc).date() # Use current UTC date if not specified

    # MODIFIED: Query top-level AMORTIZATION_SCHEDULES, filtering by entity_id and client_id
    schedules_query = db.collection("AMORTIZATION_SCHEDULES") \
                        .where("entity_id", "==", entity_id)
    if client_id: # Add client_id filter only if it's available
        schedules_query = schedules_query.where("client_id", "==", client_id)
    # Add other necessary filters, e.g., status to find schedules that are active/proposed
    schedules_query = schedules_query.where("status", "in", ["proposed", "active"]) # Example status filter

    proposed_journals_created_count = 0
    schedules_processed_count = 0

    try:
        async for schedule_doc in schedules_query.stream():
            schedules_processed_count += 1
            schedule_data = schedule_doc.to_dict()
            schedule_id = schedule_doc.id

            logger.debug(f"Entity {entity_id}: Processing schedule {schedule_id}.")

            if not schedule_data or not schedule_data.get("monthlyEntries"):
                logger.warning(f"Entity {entity_id}, Schedule {schedule_id}: No monthlyEntries found or schedule data is invalid. Skipping.")
                continue

            amortization_asset_account_code = schedule_data.get("amortizationAccountCode") # The Prepayment Asset Account
            expense_account_code = schedule_data.get("expenseAccountCode") # The Expense Account
            original_invoice_id = schedule_data.get("transactionId")
            original_line_item_id = schedule_data.get("lineItemId")
            schedule_description = schedule_data.get("description", "Amortization")

            if not all([amortization_asset_account_code, expense_account_code, original_invoice_id]):
                logger.warning(f"Entity {entity_id}, Schedule {schedule_id}: Missing critical account codes or original transactionId. Skipping journal generation for this schedule.")
                continue
            
            # Attempt to get currency from parent invoice if available (future enhancement: fetch invoice for its currency)
            # For now, using default currency from entity_settings
            currency_code = entity_settings.get("baseCurrencyCode", "USD") 
            # Example: if parent_invoice_data was passed or fetched:
            # parent_invoice_currency = parent_invoice_data.get("CurrencyCode", default_currency_code)
            # currency_code = parent_invoice_currency

            updated_monthly_entries = []
            needs_schedule_update = False

            for entry in schedule_data.get("monthlyEntries", []):
                entry_month_date_ts = entry.get("monthDate") # This is a Firestore Timestamp
                entry_amount = entry.get("amount")
                entry_status = entry.get("status", "proposed") # Default to proposed if missing
                existing_journal_id = entry.get("_system_proposedJournalId")

                if not entry_month_date_ts or entry_amount is None:
                    logger.warning(f"Entity {entity_id}, Schedule {schedule_id}: Invalid monthly entry (missing date or amount). Skipping: {entry}")
                    updated_monthly_entries.append(entry) # Keep it as is
                    continue

                entry_month_date = entry_month_date_ts.date() # Convert Firestore Timestamp to Python date

                # Check if entry is due and not yet journalized
                if entry_status == "proposed" and entry_month_date <= target_date and not existing_journal_id:
                    logger.info(f"Entity {entity_id}, Schedule {schedule_id}: Entry for {entry_month_date.strftime('%Y-%m')} is due for journalizing.")
                    
                    proposed_journal_id_val = str(uuid.uuid4()) # Renamed
                    journal_date_for_posting = datetime(entry_month_date.year, entry_month_date.month, entry_month_date.day, tzinfo=timezone.utc)
                    # Potentially adjust to month-end for journal_date if preferred:
                    # last_day_of_month = entry_month_date + relativedelta(day=31) 
                    # journal_date_for_posting = datetime(last_day_of_month.year, last_day_of_month.month, last_day_of_month.day, tzinfo=timezone.utc)

                    narration = f"{schedule_description} - {entry_month_date.strftime('%B %Y')}"
                    
                    journal_lines = [
                        {
                            "lineId": str(uuid.uuid4()),
                            "accountCode": expense_account_code,
                            "description": f"Amortization of Inv: {original_invoice_id}, Line: {original_line_item_id} for {entry_month_date.strftime('%B %Y')}",
                            "amount": entry_amount,
                            "isDebit": True,
                            "isCredit": False # Explicitly set other flag
                        },
                        {
                            "lineId": str(uuid.uuid4()),
                            "accountCode": amortization_asset_account_code, # Credit the prepayment asset account
                            "description": f"Amortization of Inv: {original_invoice_id}, Line: {original_line_item_id} for {entry_month_date.strftime('%B %Y')}",
                            "amount": entry_amount,
                            "isDebit": False, # Explicitly set other flag
                            "isCredit": True
                        }
                    ]

                    proposed_journal_data = {
                        "journal_id": proposed_journal_id_val, # Use generated UUID
                        "client_id": client_id, # Standardized
                        "entity_id": entity_id, # Standardized
                        "amortization_schedule_id": schedule_id,
                        "amortizationMonthlyEntryMonthDate": entry_month_date_ts, # Store original Firestore timestamp
                        "journalDate": journal_date_for_posting,
                        "status": "proposed", # Initial status of the journal itself
                        "narration": narration,
                        "currencyCode": currency_code, 
                        "lines": journal_lines,
                        "sourceDocumentUrl": None, # TODO: Populate if GCS URI of original invoice is available on schedule
                        "xeroManualJournalID": None,
                        "postingErrorDetails": None,
                        "created_at": firestore.SERVER_TIMESTAMP,
                        "updated_at": firestore.SERVER_TIMESTAMP,
                        "lastActionByUserId": None
                    }

                    # MODIFIED: Save to top-level PROPOSED_JOURNALS
                    proposed_journal_doc_ref = db.collection("PROPOSED_JOURNALS").document(proposed_journal_id_val)
                    await proposed_journal_doc_ref.set(proposed_journal_data)
                    logger.info(f"Entity {entity_id}, Schedule {schedule_id}: Created PROPOSED_JOURNAL {proposed_journal_id_val} for entry {entry_month_date.strftime('%Y-%m')}.")
                    proposed_journals_created_count += 1

                    # Update the entry in the schedule
                    entry["status"] = "journal_proposed"
                    entry["_system_proposedJournalId"] = proposed_journal_id_val
                    entry["lastActionTimestamp"] = datetime.now(timezone.utc) # Use Python datetime for array updates
                    needs_schedule_update = True
                
                updated_monthly_entries.append(entry)
            
            if needs_schedule_update:
                # Fix: Use db.collection() instead of schedules_query.document()
                schedule_ref = db.collection("AMORTIZATION_SCHEDULES").document(schedule_id)
                await schedule_ref.update({"monthlyEntries": updated_monthly_entries, "updated_at": firestore.SERVER_TIMESTAMP})
                logger.info(f"Entity {entity_id}: Updated monthly entries for schedule {schedule_id} with journal proposal status.")

        logger.info(f"Entity {entity_id}: Proposed journal generation finished. Processed {schedules_processed_count} schedules, created {proposed_journals_created_count} new proposed journals.")

    except Exception as e_prop_jnl:
        logger.error(f"Entity {entity_id}: Error during proposed journal generation: {e_prop_jnl}", exc_info=True)
        # Decide on error handling: re-raise, or just log and let next run attempt?

# --- End NEW: Proposed Journal Generation ---

async def _post_proposed_journals_to_xero(
    db: firestore.AsyncClient,
    client_id: Optional[str],
    entity_id: str,
    entity_settings: Dict[str, Any],
    xero_client_for_posting: XeroApiClient 
):
    logger.info(f"Starting Xero journal posting for entity {entity_id}, tenant {client_id}.")

    if not entity_settings or not entity_settings.get("clientId"):
        logger.error(f"Entity {entity_id}: Missing clientId in entity_settings or entity_settings is None. Cannot post journals.")
        return

    # MODIFIED: Query top-level PROPOSED_JOURNALS, filtering by entity_id, client_id, and status
    proposed_journals_query = db.collection("PROPOSED_JOURNALS") \
                                .where("entity_id", "==", entity_id) \
                                .where("status", "==", "proposed") # Or "pending_approval"
    if client_id:
        proposed_journals_query = proposed_journals_query.where("client_id", "==", client_id)

    journals_processed_count = 0
    journals_posted_successfully_count = 0
    journals_failed_count = 0

    try:
        async for journal_doc_snap in proposed_journals_query.stream(): # Iterate over snapshot
            journals_processed_count += 1
            proposed_journal_data = journal_doc_snap.to_dict()
            proposed_journal_firestore_id = journal_doc_snap.id
            amortization_schedule_id = proposed_journal_data.get("amortization_schedule_id")
            amortization_entry_month_date_ts = proposed_journal_data.get("amortizationMonthlyEntryMonthDate") # Firestore Timestamp

            logger.info(f"Entity {entity_id}: Attempting to post proposed journal {proposed_journal_firestore_id} to Xero.")
            
            # Convert Firestore Timestamp to ISO string for journalDate if needed by create_manual_journal
            # The create_manual_journal expects an ISO string like "2024-07-31T00:00:00Z"
            # Our PROPOSED_JOURNALS.journalDate is already a Firestore Timestamp in UTC.
            journal_date_for_xero_api = proposed_journal_data.get("journalDate") # This is a Firestore Timestamp
            if isinstance(journal_date_for_xero_api, datetime): # Ensure it's a datetime object
                proposed_journal_data["journalDate"] = journal_date_for_xero_api.isoformat().replace("+00:00", "Z")
            else:
                logger.error(f"Entity {entity_id}, Journal {proposed_journal_firestore_id}: journalDate is not a valid datetime object. Skipping.")
                # Update status to failed in Firestore
                await journal_doc_snap.reference.update({
                    "status": "posting_failed",
                    "postingErrorDetails": {"error": "InvalidDateInFirestore", "message": "journalDate was not a valid datetime object before posting."},
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
                journals_failed_count +=1
                continue

            # Determine the target status for Xero (e.g., DRAFT or POSTED)
            # For now, let's assume we always post as DRAFT. This could be configurable.
            post_as_status = "DRAFT" # Could be entity_settings.get("xeroJournalPostStatusDefault", "DRAFT")
            proposed_journal_data["status"] = post_as_status # This sets the status Xero will use

            xero_response = await xero_client_for_posting.create_manual_journal(journal_data=proposed_journal_data)

            if xero_response and xero_response.get("ManualJournals") and not xero_response.get("error"):
                # Successfully posted to Xero
                xero_manual_journal_details = xero_response["ManualJournals"][0]
                xero_manual_journal_id = xero_manual_journal_details.get("ManualJournalID")
                xero_journal_status = xero_manual_journal_details.get("Status") # e.g. DRAFT, POSTED

                logger.info(f"Entity {entity_id}: Successfully posted journal {proposed_journal_firestore_id} to Xero. Xero ID: {xero_manual_journal_id}, Xero Status: {xero_journal_status}")
                journals_posted_successfully_count += 1

                # Update PROPOSED_JOURNALS document in Firestore
                firestore_journal_update_payload = {
                    "status": f"xero_{xero_journal_status.lower()}_created", # e.g., xero_draft_created
                    "xeroManualJournalID": xero_manual_journal_id,
                    "postingErrorDetails": None,
                    "updated_at": firestore.SERVER_TIMESTAMP
                }
                await journal_doc_snap.reference.update(firestore_journal_update_payload)

                # Update corresponding monthlyEntry in AMORTIZATION_SCHEDULES
                if amortization_schedule_id and amortization_entry_month_date_ts:
                    schedule_ref = (
                        db.collection("AMORTIZATION_SCHEDULES")
                        .document(amortization_schedule_id)
                    )
                    schedule_doc_snapshot = await schedule_ref.get()
                    if schedule_doc_snapshot.exists:
                        schedule_data = schedule_doc_snapshot.to_dict()
                        updated_entries = []
                        entry_updated = False
                        for entry in schedule_data.get("monthlyEntries", []):
                            # Compare Firestore Timestamps directly
                            if entry.get("monthDate") == amortization_entry_month_date_ts:
                                entry["status"] = f"xero_{xero_journal_status.lower()}_created"
                                entry["postedJournalId"] = xero_manual_journal_id # Using Xero's ID
                                entry["lastActionTimestamp"] = datetime.now(timezone.utc)
                                entry_updated = True
                            updated_entries.append(entry)
                        
                        if entry_updated:
                            await schedule_ref.update({"monthlyEntries": updated_entries, "updated_at": firestore.SERVER_TIMESTAMP})
                            logger.info(f"Entity {entity_id}: Updated amortization schedule {amortization_schedule_id} for entry linked to journal {proposed_journal_firestore_id}.")
                    else:
                        logger.warning(f"Entity {entity_id}: Amortization schedule {amortization_schedule_id} not found for journal {proposed_journal_firestore_id}.")
                else:
                    logger.warning(f"Entity {entity_id}: Missing amortizationScheduleId or amortizationMonthlyEntryMonthDate for journal {proposed_journal_firestore_id}. Cannot update schedule.")

            else:
                # Posting failed
                journals_failed_count += 1
                error_details = xero_response if xero_response else {"error": "UnknownError", "message": "No response from Xero client or empty response."}
                logger.error(f"Entity {entity_id}: Failed to post journal {proposed_journal_firestore_id} to Xero. Details: {error_details}")
                await journal_doc_snap.reference.update({
                    "status": "posting_failed",
                    "postingErrorDetails": error_details,
                    "updated_at": firestore.SERVER_TIMESTAMP
                })
        
        logger.info(f"Entity {entity_id}: Xero journal posting finished. Processed: {journals_processed_count}, Succeeded: {journals_posted_successfully_count}, Failed: {journals_failed_count}.")

    except Exception as e_post_jnl:
        logger.error(f"Entity {entity_id}: Error during Xero journal posting process: {e_post_jnl}", exc_info=True)


# --- Main execution block for local testing ---
if __name__ == "__main__":
    # Configure logging for the local runner
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
    logger.info("Starting local Pub/Sub subscriber to listen to Google Cloud subscription...")

    # For direct testing, use hardcoded full paths to the subscription and topic
    SUBSCRIPTION_FULL_PATH = "projects/drcr-d660a/subscriptions/xero-sync-topic-sub"
    TOPIC_FULL_PATH = "projects/drcr-d660a/topics/xero-sync-topic"
    
    logger.info(f"Using subscription path: {SUBSCRIPTION_FULL_PATH}")
    logger.info(f"Using topic path: {TOPIC_FULL_PATH}")
    
    # GCP_PROJECT_ID is still needed for some operations
    GCP_PROJECT_ID = os.getenv("GCP_PROJECT_ID", "drcr-d660a")

    # Ensure google.cloud.pubsub_v1 is imported at the top of your main.py file
    from google.cloud import pubsub_v1 
    # Ensure asyncio is imported at the top of your main.py file
    # import asyncio
    # Ensure datetime and timezone are imported for context
    # from datetime import datetime, timezone

    subscription_path = SUBSCRIPTION_FULL_PATH
    
    subscriber = pubsub_v1.SubscriberClient()

    # Define the callback function that will process received messages
    async def process_pubsub_message(message: pubsub_v1.subscriber.message.Message) -> None:
        logger.info(f"Received Pub/Sub message ID: {message.message_id}, Published at: {message.publish_time}")
        
        # This is a workaround for the Pub/Sub message handler
        # Instead of trying to use the Cloud Function interface directly,
        # we'll manually decode the message and call the function with proper parameters
        try:
            # The message.data contains the raw JSON string as bytes
            message_json = json.loads(message.data.decode('utf-8'))
            logger.info(f"Decoded message: {message_json}")
            
            # Create a simulated context
            class SimpleContext:
                def __init__(self):
                    self.event_id = message.message_id
                    self.timestamp = message.publish_time.isoformat() if message.publish_time else datetime.now(timezone.utc).isoformat()
                    self.resource = {
                        "service": "pubsub.googleapis.com",
                        "name": TOPIC_FULL_PATH,
                        "type": "pubsub"
                    }
            
            context = SimpleContext()
            
            # Re-encode the JSON for xero_sync_consumer which expects base64 encoded data
            # Create event object in the format that xero_sync_consumer expects
            event_for_consumer = {
                "data": base64.b64encode(json.dumps(message_json).encode('utf-8'))
            }
            logger.info(f"Calling xero_sync_consumer with base64-encoded data")
            
            # Call xero_sync_consumer with properly formatted data
            await xero_sync_consumer(event_for_consumer, context)
            logger.info(f"Successfully processed message ID: {message.message_id}")
            message.ack()
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON from message: {e}. Message data: {message.data[:200]}")
            message.nack()
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            import traceback
            traceback.print_exc()
            message.nack()

        # Note: The message processing is now done directly in the try/except block above
        # We don't need the old processing code anymore

    # The callback `process_pubsub_message` is async.
    # The `subscriber.subscribe` method handles running this async callback.
    # We wrap the call to the async callback in asyncio.create_task or ensure the event loop handles it.
    def callback_wrapper(message: pubsub_v1.subscriber.message.Message):
        # Create a new event loop for the thread if one doesn't exist
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
        
        # Run the coroutine in this thread's event loop
        loop.run_until_complete(process_pubsub_message(message))


    streaming_pull_future = subscriber.subscribe(subscription_path, callback=callback_wrapper)
    
    logger.info(f"Listening for messages on {subscription_path}... Press Ctrl+C to exit.")

    # REMOVE the old loop = asyncio.get_event_loop() and loop.run_forever() block
    # REPLACE with the following:
    try:
        # streaming_pull_future.result() will block here until the future is cancelled
        # or an unrecoverable error occurs in the subscriber's connection.
        streaming_pull_future.result()
    except KeyboardInterrupt:
        logger.info("KeyboardInterrupt received, shutting down subscriber...")
    except Exception as e: # Catch other exceptions that might terminate the future
        logger.error(f"Subscriber future terminated with an error: {e}", exc_info=True)
    finally:
        logger.info("Shutting down streaming_pull_future...")
        streaming_pull_future.cancel()  # Signal the subscriber to stop
        try:
            # Wait for the subscriber to clean up (optional timeout)
            streaming_pull_future.result(timeout=30) 
            logger.info("Streaming pull future successfully shut down.")
        except TimeoutError:
            logger.warning("Timeout waiting for streaming pull future to shut down.")
        except asyncio.CancelledError: # Future might be cancelled by the time we check result()
            logger.info("Streaming pull future was cancelled as expected.")
        except Exception as e_spf: # Catch other exceptions from future.result()
            logger.error(f"Error during streaming_pull_future final shutdown: {e_spf}", exc_info=True)
        
        # Close the subscriber client
        subscriber.close()
        logger.info("Pub/Sub subscriber client closed.")

    # --- Ensure old direct test invocation is commented out or removed ---

    logger.info("Local Pub/Sub listener script finished.")


    if not os.getenv("GCS_BUCKET_NAME"):
        logger.warning(
            "Local test: GCS_BUCKET_NAME is not set in .env. GCS uploads will be skipped or fail if attempted."
        )
