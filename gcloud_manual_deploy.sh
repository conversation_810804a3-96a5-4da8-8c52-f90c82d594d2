# Step 1: Build Docker image
docker build -t drcr_backend .

# Step 2: Authenticate Google Cloud SDK
gcloud auth login

# Step 3: Set desired project
gcloud config set project drcr-d660a

# Step 4: Tag Docker image
docker tag drcr_backend gcr.io/drcr-d660a/drcr_backend

# Step 5: Push Docker image to Google Container Registry
docker push gcr.io/drcr-d660a/drcr_backend

# Step 6: Deploy to Google Cloud Run
gcloud run deploy drcr-backend --image gcr.io/drcr-d660a/drcr_backend --platform managed --region europe-west2 --allow-unauthenticated --port 8080