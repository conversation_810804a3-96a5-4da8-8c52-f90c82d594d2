import asyncio
from google.cloud import firestore

async def fix_entity_status():
    """Fix entity status that's stuck in 'syncing' state"""
    db = firestore.AsyncClient()
    
    entity_id = 'a8e46b01-de7d-42bb-ab01-d1a395573d51'
    
    try:
        entity_ref = db.collection("ENTITIES").document(entity_id)
        await entity_ref.update({
            "status": "active",
            "updated_at": firestore.SERVER_TIMESTAMP
        })
        print(f"✅ Updated entity {entity_id} status to 'active'")
    except Exception as e:
        print(f"❌ Failed to update entity status: {e}")
    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(fix_entity_status()) 