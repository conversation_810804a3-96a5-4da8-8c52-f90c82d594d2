import os
import json
import base64
import httpx
import tempfile
import logging
from typing import Dict, Any, Optional, Union
import re # For normalizing numbers
from datetime import datetime

from PIL import Image, ImageEnhance
import cv2
import numpy as np
import openai # For OpenAI API calls
from io import BytesIO # For handling bytes as files for OpenAI

# Mistral client is not directly available via pip install mistralai,
# but httpx can be used to call its API endpoints as shown in your script.

logger = logging.getLogger(__name__)

# === Preprocessing parameters ===
PREPROCESS_CONTRAST = 1.5
PREPROCESS_DENOISE_H = 10
PREPROCESS_THRESHOLD = False
PREPROCESS_SHARPEN = False

# Mistral API Configuration
MISTRAL_API_BASE_URL = "https://api.mistral.ai/v1"

# Environment Variables for API Keys and Model Configs
MISTRAL_OCR_MODEL = os.getenv("MISTRAL_OCR_MODEL", "mistral-small-latest")
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
# Adjusted to look for OPENAI_MODEL from .env, defaulting to gpt-4o if not found
OPENAI_EXTRACTION_MODEL = os.getenv("OPENAI_MODEL", "gpt-4.1-mini")

# ---- REMOVED module-level DEBUG_DP prints to avoid interference ----

# Initialize OpenAI client (do this once at the module level)
openai_client = None # Initialize to None first
if OPENAI_API_KEY:
    # ---- Add temporary print here to confirm key and client init ----
    print(f"DEBUG_DP_INIT: OPENAI_API_KEY found at module level: '{OPENAI_API_KEY[:5]}...'") # Print first 5 chars for confirmation
    try:
        openai_client = openai.AsyncOpenAI(api_key=OPENAI_API_KEY)
        print(f"DEBUG_DP_INIT: openai_client initialized: {openai_client is not None}")
    except Exception as e_client_init:
        print(f"DEBUG_DP_INIT: ERROR initializing openai_client: {e_client_init}")
else:
    print("DEBUG_DP_INIT: OPENAI_API_KEY NOT found at module level.")

def preprocess_image_for_ocr(input_bytes: bytes, contrast=PREPROCESS_CONTRAST, denoise_h=PREPROCESS_DENOISE_H, threshold_enabled=PREPROCESS_THRESHOLD, sharpen_enabled=PREPROCESS_SHARPEN) -> bytes:
    """
    Preprocess an image (from bytes) for OCR: grayscale, contrast, denoise, optional thresholding and sharpening.
    Returns processed image as bytes in PNG format.
    """
    try:
        with tempfile.NamedTemporaryFile(delete=False, suffix=".png") as tmp_in, \
             tempfile.NamedTemporaryFile(delete=False, suffix=".png") as tmp_out:
            tmp_in.write(input_bytes)
            tmp_in_path = tmp_in.name
            tmp_out_path = tmp_out.name
        
        image = Image.open(tmp_in_path).convert('L')
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(contrast)
        image.save(tmp_out_path, format="PNG") # Save intermediate for cv2

        img_cv = cv2.imread(tmp_out_path, cv2.IMREAD_GRAYSCALE)
        if img_cv is None:
            raise ValueError("cv2.imread failed to load the image from temporary path.")

        img_denoised = cv2.fastNlMeansDenoising(img_cv, None, h=denoise_h)

        if threshold_enabled:
            _, img_denoised = cv2.threshold(img_denoised, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)

        if sharpen_enabled:
            kernel = np.array([[0, -1, 0], [-1, 5,-1], [0, -1, 0]])
            img_denoised = cv2.filter2D(img_denoised, -1, kernel)

        cv2.imwrite(tmp_out_path, img_denoised)
        
        with open(tmp_out_path, "rb") as f_out:
            processed_bytes = f_out.read()
            
        return processed_bytes
    except Exception as e:
        logger.error(f"Error during image preprocessing: {e}")
        raise
    finally:
        if 'tmp_in_path' in locals() and os.path.exists(tmp_in_path):
            os.remove(tmp_in_path)
        if 'tmp_out_path' in locals() and os.path.exists(tmp_out_path):
            os.remove(tmp_out_path)


async def process_pdf_with_mistral_ocr(
    pdf_bytes: bytes, 
    api_key: str, 
    file_name: str = "document.pdf"
) -> Optional[Dict[str, Any]]:
    """Process a PDF (from bytes) with Mistral's OCR capabilities using the provided API key."""
    if not api_key:
        logger.error("Mistral API key was not provided to process_pdf_with_mistral_ocr.")
        return None
        
    logger.info(f"Processing PDF with Mistral OCR: {file_name}")
    headers = {"Authorization": f"Bearer {api_key}"}
    
    async with httpx.AsyncClient(timeout=120.0) as client: 
        try:
            # Step 1: Upload the file
            files = {"file": (file_name, pdf_bytes, "application/pdf")}
            upload_response = await client.post(
                f"{MISTRAL_API_BASE_URL}/files",
                headers=headers,
                files=files,
                data={"purpose": "ocr"}
            )
            upload_response.raise_for_status()
            file_id = upload_response.json()["id"]
            logger.info(f"File {file_name} uploaded successfully to Mistral. ID: {file_id}")

            # Step 2: Get a signed URL for the file for /ocr endpoint
            url_response = await client.get(f"{MISTRAL_API_BASE_URL}/files/{file_id}/url", headers=headers)
            url_response.raise_for_status()
            signed_url = url_response.json()["url"]
            logger.info(f"Got signed URL for Mistral file ID {file_id}")

            # Step 3: Process with OCR using the /ocr endpoint and signed URL
            ocr_payload_with_url = {
                 "model": "mistral-ocr-latest",
                 "document": {
                     "type": "document_url",
                     "document_url": signed_url
                 }
            }
            
            ocr_response = await client.post(
                f"{MISTRAL_API_BASE_URL}/ocr", 
                headers=headers,
                json=ocr_payload_with_url
            )
            ocr_response.raise_for_status()
            ocr_data = ocr_response.json()

            all_text = "".join([page["markdown"] + "\n\n" for page in ocr_data.get("pages", [])])
            num_pages = len(ocr_data.get("pages", []))
            logger.info(f"Successfully processed {file_name} with Mistral OCR: {num_pages} pages.")
            
            return {"text": all_text, "num_pages": num_pages, "file_name": file_name}

        except httpx.HTTPStatusError as e:
            logger.error(f"Mistral API HTTP error for {file_name}: {e.response.status_code} - {e.response.text}")
            # Return None to allow main flow to continue gracefully
        except Exception as e:
            logger.error(f"Error processing PDF {file_name} with Mistral OCR: {e}", exc_info=True)
            # Return None
        finally:
            # Step 4: Delete the uploaded file from Mistral
            if 'file_id' in locals() and file_id:
                try:
                    await client.delete(f"{MISTRAL_API_BASE_URL}/files/{file_id}", headers=headers)
                    logger.info(f"Deleted file {file_id} ({file_name}) from Mistral servers.")
                except Exception as del_e:
                    logger.warning(f"Could not delete file {file_id} from Mistral: {del_e}")
    return None


async def process_image_with_mistral_ocr(
    image_bytes: bytes, 
    api_key: str,
    file_name: str = "image.png"
) -> Optional[Dict[str, Any]]:
    """Process an image (from bytes) with Mistral's OCR using the provided API key."""
    if not api_key:
        logger.error("Mistral API key was not provided to process_image_with_mistral_ocr.")
        return None
        
    logger.info(f"Processing image with Mistral OCR: {file_name}")
    try:
        preprocessed_image_bytes = preprocess_image_for_ocr(image_bytes)
        base64_image = base64.b64encode(preprocessed_image_bytes).decode('utf-8')
        mime_type = "image/png" 
        
        headers = {"Authorization": f"Bearer {api_key}"}
        ocr_payload = {
            "model": "mistral-ocr-latest",
            "document": {
                "type": "image_url", 
                "image_url": f"data:{mime_type};base64,{base64_image}"
            }
        }

        async with httpx.AsyncClient(timeout=60.0) as client:
            ocr_response = await client.post(
                f"{MISTRAL_API_BASE_URL}/ocr",
                headers=headers,
                json=ocr_payload
            )
            ocr_response.raise_for_status()
            ocr_data = ocr_response.json()

        all_text = "".join([page["markdown"] + "\n\n" for page in ocr_data.get("pages", [])])
        num_pages = len(ocr_data.get("pages", []))
        logger.info(f"Successfully processed {file_name} with Mistral OCR.")

        return {"text": all_text, "num_pages": num_pages, "file_name": file_name}

    except httpx.HTTPStatusError as e:
        logger.error(f"Mistral API HTTP error for image {file_name}: {e.response.status_code} - {e.response.text}")
        # Return None
    except Exception as e:
        logger.error(f"Error processing image {file_name} with Mistral OCR: {e}", exc_info=True)
        # Return None
    return None 

# --- OpenAI Configuration ---
OPENAI_API_BASE_URL = "https://api.openai.com/v1"

def get_llm_extraction_prompt() -> str:
    """Returns the standardized LLM extraction prompt."""
    # This is adapted from your provided script, ensure it matches your latest requirements.
    return (
        "Extract the following fields from this invoice attachment: InvoiceNumber, Date, Total, BillingPeriod, ServicePeriod, a LineItems array, where each item has Description, TotalExcludingVAT (the net amount, if available), and TotalIncludingVAT (the gross amount, if available). For each line item, extract the total exactly as printed on the invoice. Never calculate or infer the total from quantity, rate, or other fields. If the total is not explicitly shown, set the field to null and add a Note explaining why. For all totals, output only the final numeric value (e.g., 349.89), never a calculation, formula, or string expression. If only one of these totals is present, set the other to null. If VAT is not mentioned, extract the value as both fields and set a note if you are unsure. For BillingPeriod and ServicePeriod, always extract as a JSON object with Start and End fields in YYYY-MM-DD format, e.g. {\"Start\": \"2025-04-01\", \"End\": \"2025-04-30\"}. If not present, return null for that field. Do NOT return a string for BillingPeriod or ServicePeriod. In addition, determine if this invoice is a prepayment (i.e., for a period longer than 32 days). Add a field 'LLM_IsPrepayment' (true/false/null) and a field 'LLM_PrepaymentReason' explaining your reasoning. For all monetary fields (such as Total and LineItems totals), extract only the numeric value without any currency symbols or codes.\n\n"
        "**Service Period Inference (Supplementary):**\n"
        "If explicit 'BillingPeriod' or 'ServicePeriod' with clear YYYY-MM-DD start and end dates are NOT found, attempt to infer the service period from other context in the document. Look for keywords in line item descriptions or general text (e.g., 'annual subscription,' 'quarterly service,' 'valid for 6 months,' 'term: 1 year,' dates written in prose like 'January to March 2024').\n"
        "If you can infer a service period this way, provide the following supplementary fields:\n"
        "- 'LLM_InferredServicePeriod': A JSON object with 'Start' and 'End' fields in YYYY-MM-DD format if you can determine both (e.g., by using the invoice 'Date' as a start and calculating the end from a duration). If only a duration is found, you might estimate the start and end based on the invoice 'Date'.\n"
        "- 'LLM_InferredDurationNotice': A string capturing the textual evidence of the duration (e.g., 'annual', '3 months', 'for the year 2024').\n"
        "- 'LLM_PeriodInferenceMethod': A brief string describing how you inferred the period (e.g., 'keyword \'annual\' in line item', 'phrase \'service for 6 months\' in description', 'date range in prose').\n"
        "- 'LLM_PeriodInferenceConfidence': Your confidence in this inference ('high', 'medium', or 'low').\n"
        "Only populate these 'LLM_Inferred...' fields if the primary 'BillingPeriod' or 'ServicePeriod' fields are null or incomplete. Prioritize explicit, well-formatted dates for 'BillingPeriod'/'ServicePeriod' when available.\n\n"
        "Respond ONLY with a single JSON object and nothing else.\n\n"
        "Example output:\n"
        "{\n"
        "  \"InvoiceNumber\": \"INV-12345\",\n"
        "  \"Date\": \"2025-04-01\",\n"
        "  \"Total\": 1234.56,\n"
        "  \"BillingPeriod\": {\"Start\": \"2025-04-01\", \"End\": \"2025-04-30\"}, \"ServicePeriod\": null,\n"
        "  \"LineItems\": [\n"
        "    {\"Description\": \"Widget A\", \"TotalExcludingVAT\": 100.00, \"TotalIncludingVAT\": 120.00, \"Note\": null},\n"
        "    {\"Description\": \"Widget B\", \"TotalExcludingVAT\": null, \"TotalIncludingVAT\": null, \"Note\": \"No total shown for this line item.\"}\n"
        "  ],\n"
        "  \"LLM_IsPrepayment\": false,\n"
        "  \"LLM_PrepaymentReason\": \"Billing period is 29 days, so it is not a prepayment.\",\n"
        "  \"LLM_InferredServicePeriod\": null, /* Populated if Billing/ServicePeriod is null and inference is made */\n"
        "  \"LLM_InferredDurationNotice\": null,\n"
        "  \"LLM_PeriodInferenceMethod\": null,\n"
        "  \"LLM_PeriodInferenceConfidence\": null\n"
        "}"
    )

async def extract_data_with_openai_direct(
    attachment_bytes: bytes, 
    mime_type: str, 
    file_name_for_logging: str
) -> Optional[Dict[str, Any]]:
    """
    Attempts to extract structured data directly from an image or PDF using OpenAI.
    Fetches credentials and model from environment variables INSIDE the function.
    Initializes client if needed.
    """
    openai_api_key = os.getenv("OPENAI_API_KEY")
    openai_extraction_model = os.getenv("OPENAI_MODEL", "gpt-4.1-mini") # Use OPENAI_MODEL from env

    if not openai_api_key:
        logger.error("EXTRACT_DIRECT: OPENAI_API_KEY not found in environment.")
        return None
    if not openai_extraction_model:
         logger.error("EXTRACT_DIRECT: OPENAI_MODEL not found in environment.")
         return None

    logger.info(f"EXTRACT_DIRECT: Attempting direct OpenAI extraction for {file_name_for_logging} (MIME: {mime_type}) using model {openai_extraction_model}")
    
    # Initialize client here
    try:
        openai_client = openai.AsyncOpenAI(api_key=openai_api_key)
    except Exception as e_client_init:
        logger.error(f"EXTRACT_DIRECT: Failed to initialize OpenAI client: {e_client_init}", exc_info=True)
        return None

    messages_content = [] 
    extraction_prompt_text = get_llm_extraction_prompt()
    uploaded_file_id_for_cleanup = None

    try:
        if mime_type.startswith("image/"):
            base64_image = base64.b64encode(attachment_bytes).decode("utf-8")
            image_url = f"data:{mime_type};base64,{base64_image}"
            messages_content.append({"type": "image_url", "image_url": {"url": image_url}})
            messages_content.append({"type": "text", "text": extraction_prompt_text})

        elif mime_type == "application/pdf":
            pdf_file_obj = BytesIO(attachment_bytes)
            logger.info(f"EXTRACT_DIRECT: Uploading PDF {file_name_for_logging} to OpenAI File API for direct processing.")
            try:
                uploaded_file = await openai_client.files.create(
                    file=(file_name_for_logging, pdf_file_obj),
                    purpose="user_data" 
                )
                uploaded_file_id_for_cleanup = uploaded_file.id
                logger.info(f"EXTRACT_DIRECT: Successfully uploaded PDF {file_name_for_logging} to OpenAI, File ID: {uploaded_file_id_for_cleanup}")
                
                prompt_with_file_id_ref = (
                    f"{extraction_prompt_text}\n\n"
                    f"Please analyze the content of the PDF document associated with File ID: {uploaded_file_id_for_cleanup} to extract the requested information."
                )
                messages_content.append({"type": "text", "text": prompt_with_file_id_ref})

            except Exception as upload_err:
                logger.error(f"EXTRACT_DIRECT: Error uploading PDF {file_name_for_logging} to OpenAI: {upload_err}", exc_info=True)
                # Clean up if upload failed but ID was somehow assigned (unlikely but safe)
                if uploaded_file_id_for_cleanup:
                     try: await openai_client.files.delete(uploaded_file_id_for_cleanup) 
                     except Exception: pass
                return None
        else:
            logger.warning(f"EXTRACT_DIRECT: Unsupported MIME type for direct OpenAI extraction: {mime_type} for file {file_name_for_logging}")
            return None

        if not messages_content:
             logger.warning(f"EXTRACT_DIRECT: No messages content formulated for OpenAI direct extraction for {file_name_for_logging}")
             return None

        messages = [{"role": "user", "content": messages_content}]

        completion = await openai_client.chat.completions.create(
            model=openai_extraction_model,
            messages=messages,
        )
        
        response_content = completion.choices[0].message.content
        if not response_content:
            logger.warning(f"EXTRACT_DIRECT: OpenAI direct extraction for {file_name_for_logging} returned no content.")
            return None

        try:
            match = re.search(r"\{.*\}", response_content, re.DOTALL)
            if match:
                json_str = match.group(0)
                extracted_data = json.loads(json_str)
                logger.info(f"EXTRACT_DIRECT: Successfully parsed JSON from OpenAI direct extraction for {file_name_for_logging}")
                return extracted_data
            else:
                logger.error(f"EXTRACT_DIRECT: Could not find JSON object in OpenAI direct response for {file_name_for_logging}. Response: {response_content}")
                return None
        except json.JSONDecodeError as e:
            logger.error(f"EXTRACT_DIRECT: Failed to parse JSON from OpenAI direct response for {file_name_for_logging}: {e}. Response: {response_content}")
            return None

    except openai.APIError as e:
        logger.error(f"EXTRACT_DIRECT: OpenAI API error during direct extraction for {file_name_for_logging}: {e}")
    except Exception as e:
        logger.error(f"EXTRACT_DIRECT: Unexpected error during direct OpenAI extraction for {file_name_for_logging}: {e}", exc_info=True)
    finally:
        # Ensure cleanup happens even if completion/parsing fails after successful upload
        if uploaded_file_id_for_cleanup:
            try:
                logger.info(f"EXTRACT_DIRECT: Attempting to delete uploaded file {uploaded_file_id_for_cleanup} from OpenAI.")
                # Need a client instance for cleanup, re-init if necessary (though unlikely needed here)
                if openai_client: 
                    await openai_client.files.delete(uploaded_file_id_for_cleanup)
                    logger.info(f"EXTRACT_DIRECT: Successfully deleted file {uploaded_file_id_for_cleanup} from OpenAI.")
                else:
                    # Could potentially initialize a temporary client just for deletion if key is available
                    temp_openai_api_key = os.getenv("OPENAI_API_KEY")
                    if temp_openai_api_key:
                         temp_client = openai.AsyncOpenAI(api_key=temp_openai_api_key)
                         await temp_client.files.delete(uploaded_file_id_for_cleanup)
                         logger.info(f"EXTRACT_DIRECT: Successfully deleted file {uploaded_file_id_for_cleanup} via temp client.")
                    else:
                         logger.warning(f"EXTRACT_DIRECT: Could not re-initialize client to delete file {uploaded_file_id_for_cleanup}.")
            except Exception as del_err:
                logger.warning(f"EXTRACT_DIRECT: Failed to delete file {uploaded_file_id_for_cleanup} from OpenAI: {del_err}", exc_info=True)
    
    return None

async def extract_structured_data_with_openai(
    text_content: str,
    prompt: Optional[str] = None,
    file_name_for_logging: str = "document"
) -> Optional[Dict[str, Any]]:
    """
    Extracts structured data from text using OpenAI's Chat Completions API.
    Fetches credentials and model from environment variables INSIDE the function.
    Initializes client if needed.
    """
    openai_api_key = os.getenv("OPENAI_API_KEY")
    openai_extraction_model = os.getenv("OPENAI_MODEL", "gpt-4.1-mini") # Use OPENAI_MODEL from env

    if not openai_api_key:
        logger.error("EXTRACT_STRUCTURED: OPENAI_API_KEY not found in environment.")
        return None
    if not openai_extraction_model:
         logger.error("EXTRACT_STRUCTURED: OPENAI_MODEL not found in environment.")
         return None
    if not text_content:
        logger.warning(f"EXTRACT_STRUCTURED: No text content provided for OpenAI extraction from {file_name_for_logging}.")
        return None

    logger.info(f"EXTRACT_STRUCTURED: Attempting to extract structured data from text for {file_name_for_logging} using OpenAI model {openai_extraction_model}.")

    # Initialize client here
    try:
        openai_client = openai.AsyncOpenAI(api_key=openai_api_key)
    except Exception as e_client_init:
        logger.error(f"EXTRACT_STRUCTURED: Failed to initialize OpenAI client: {e_client_init}", exc_info=True)
        return None

    extraction_prompt = prompt or get_llm_extraction_prompt()
    
    messages = [
        {"role": "system", "content": "You are an expert data extraction assistant. Respond only with the requested JSON object."},
        {"role": "user", "content": f"{extraction_prompt}\n\nDocument content (from OCR or other text source):\n{text_content}"}
    ]
    
    try:
        completion = await openai_client.chat.completions.create(
            model=openai_extraction_model,
            messages=messages,
            response_format={"type": "json_object"} 
        )
        response_content = completion.choices[0].message.content
        if not response_content:
            logger.warning(f"EXTRACT_STRUCTURED: OpenAI response content was empty for {file_name_for_logging}.")
            return None
        
        try:
            extracted_data = json.loads(response_content)
            logger.info(f"EXTRACT_STRUCTURED: Successfully extracted structured data from {file_name_for_logging} using OpenAI.")
            return extracted_data
        except json.JSONDecodeError as e:
            match = re.search(r"\{.*\}", response_content, re.DOTALL)
            if match:
                json_str = match.group(0)
                logger.warning(f"EXTRACT_STRUCTURED: Had to use regex to find JSON in OpenAI text response for {file_name_for_logging}. Original response: {response_content}")
                return json.loads(json_str)
            logger.error(f"EXTRACT_STRUCTURED: Failed to parse JSON from OpenAI response for {file_name_for_logging}: {e}. Response: {response_content}")
            return None

    except openai.APIError as e:
        logger.error(f"EXTRACT_STRUCTURED: OpenAI API error during text extraction for {file_name_for_logging}: {e}")
    except Exception as e:
        logger.error(f"EXTRACT_STRUCTURED: Unexpected error during OpenAI text extraction for {file_name_for_logging}: {e}", exc_info=True)
    return None

# --- Post-processing and Validation --- 
def _normalize_to_float(value: Any) -> Optional[float]:
    if value is None:
        return None
    try:
        # Remove currency symbols, commas, etc., keep decimal point and minus sign
        normalized_str = re.sub(r"[^\d.\-]", "", str(value))
        return float(normalized_str)
    except (ValueError, TypeError):
        return None

async def post_process_extracted_invoice_data(
    extracted_data: Dict[str, Any],
    xero_transaction_record: Dict[str, Any], # The original record from Xero API
    file_name_for_logging: str = "document"
) -> Dict[str, Any]:
    """
    Validates and enriches LLM-extracted invoice data against the original Xero transaction.
    - Validates extracted totals via direct comparison.
    - Assesses prepayment status based on date ranges.
    - Sets ExpectedServiceStartDate/EndDate.
    """
    logger.info(f"Post-processing extracted data for {file_name_for_logging} via direct comparison method.")

    processed_data = extracted_data.copy() # Start with a copy of LLM output
    tolerance = 0.02 # e.g., 2 cents

    # --- Direct Total Validation ---
    xero_total_amount = _normalize_to_float(xero_transaction_record.get("Total"))
    llm_total = _normalize_to_float(processed_data.get("Total"))

    match_status = None
    validation_note = ""

    if llm_total is not None and xero_total_amount is not None:
        if abs(llm_total - xero_total_amount) <= tolerance:
            match_status = True
            validation_note = "LLM extracted Total matches Xero Total."
            logger.info(f"[{file_name_for_logging}] LLM Total validation: Match (LLM: {llm_total}, Xero: {xero_total_amount})")
        else:
            match_status = False
            validation_note = "LLM extracted Total does NOT match Xero Total."
            logger.warning(f"[{file_name_for_logging}] LLM Total validation: MISMATCH (LLM: {llm_total}, Xero: {xero_total_amount})")
    elif llm_total is None:
        match_status = False # Consider missing LLM total a mismatch
        validation_note = "LLM did not extract a 'Total'."
        logger.warning(f"[{file_name_for_logging}] LLM Total validation: LLM 'Total' is missing.")
    else: # xero_total_amount is None
        match_status = None # Cannot determine match if Xero total is missing
        validation_note = "Xero record 'Total' is missing. Cannot validate."
        logger.warning(f"[{file_name_for_logging}] LLM Total validation: Xero 'Total' is missing.")

    processed_data["System_TotalValidation"] = {
        "match": match_status,
        "llm_extracted_total": llm_total,
        "xero_total_amount": xero_total_amount, # Store the directly compared Xero total
        "note": validation_note
    }
    # --- End Direct Total Validation ---

    # --- Prepayment Assessment (based on LLM extracted dates) - UNCHANGED ---
    billing_period = processed_data.get("BillingPeriod")
    service_period = processed_data.get("ServicePeriod")
    period_to_check = billing_period or service_period

    processed_data["System_IsPrepayment"] = None
    processed_data["System_PrepaymentReason"] = "No valid period found in LLM extraction."

    if isinstance(period_to_check, dict) and period_to_check.get("Start") and period_to_check.get("End"):
        try:
            start_date_str = period_to_check["Start"]
            end_date_str = period_to_check["End"]
            start_date = datetime.strptime(start_date_str, "%Y-%m-%d")
            end_date = datetime.strptime(end_date_str, "%Y-%m-%d")

            num_days = (end_date - start_date).days + 1 # Add 1 for inclusive period length

            if num_days > 32: # Your definition of prepayment
                processed_data["System_IsPrepayment"] = True
                processed_data["System_PrepaymentReason"] = f"Period ({start_date_str} to {end_date_str}) is {num_days} days (>32)."
                logger.info(f"[{file_name_for_logging}] System prepayment check: True, {num_days} days.")
            else:
                processed_data["System_IsPrepayment"] = False
                processed_data["System_PrepaymentReason"] = f"Period ({start_date_str} to {end_date_str}) is {num_days} days (<=32)."
                logger.info(f"[{file_name_for_logging}] System prepayment check: False, {num_days} days.")

        except (ValueError, TypeError) as e: # Added TypeError for safety with date operations
            processed_data["System_PrepaymentReason"] = f"Could not parse dates from period {period_to_check}: {e}"
            logger.warning(f"[{file_name_for_logging}] Date parsing error for prepayment check: {e}")
    else:
        logger.info(f"[{file_name_for_logging}] No BillingPeriod or ServicePeriod with Start/End dates found in LLM data for system prepayment check.")

    if "LLM_IsPrepayment" in processed_data:
        logger.info(f"[{file_name_for_logging}] LLM's prepayment assessment: {processed_data.get('LLM_IsPrepayment')}, Reason: {processed_data.get('LLM_PrepaymentReason')}")
    # --- End Prepayment Assessment ---

    # --- Add top-level ExpectedServiceStartDate/EndDate for main.py compatibility - UNCHANGED ---
    # --- MODIFIED: Incorporate LLM_InferredServicePeriod ---
    processed_data["ExpectedServiceStartDate"] = None
    processed_data["ExpectedServiceEndDate"] = None
    processed_data["_system_servicePeriodSource"] = None # Initialize
    processed_data["_system_servicePeriodInferenceConfidence"] = None # Initialize

    chosen_period = None
    period_source_log_msg = "No explicit or inferred period found."

    # 1. Prioritize explicit ServicePeriod
    service_period = processed_data.get("ServicePeriod")
    if isinstance(service_period, dict) and service_period.get("Start") and service_period.get("End"):
        chosen_period = service_period
        period_source_log_msg = f"LLM's explicit ServicePeriod: {service_period.get('Start')} - {service_period.get('End')}"
        processed_data["_system_servicePeriodSource"] = "llm_explicit_service_period"
        processed_data["_system_servicePeriodInferenceConfidence"] = "high"  # Explicit periods get high confidence

    # 2. Else, try explicit BillingPeriod
    if not chosen_period:
        billing_period = processed_data.get("BillingPeriod")
        if isinstance(billing_period, dict) and billing_period.get("Start") and billing_period.get("End"):
            chosen_period = billing_period
            period_source_log_msg = f"LLM's explicit BillingPeriod: {billing_period.get('Start')} - {billing_period.get('End')}"
            processed_data["_system_servicePeriodSource"] = "llm_explicit_billing_period"
            processed_data["_system_servicePeriodInferenceConfidence"] = "high"  # Explicit periods get high confidence

    # 3. Else, try LLM_InferredServicePeriod (if confidence is acceptable)
    if not chosen_period:
        llm_inferred_period = processed_data.get("LLM_InferredServicePeriod")
        llm_inferred_confidence = processed_data.get("LLM_PeriodInferenceConfidence")
        # Define what constitutes acceptable confidence, e.g., 'high' or 'medium'
        acceptable_confidence = ["high", "medium"]
        if isinstance(llm_inferred_period, dict) and \
           llm_inferred_period.get("Start") and llm_inferred_period.get("End") and \
           llm_inferred_confidence and llm_inferred_confidence.lower() in acceptable_confidence:
            
            chosen_period = llm_inferred_period
            period_source_log_msg = f"LLM's InferredServicePeriod (Confidence: {llm_inferred_confidence}): {llm_inferred_period.get('Start')} - {llm_inferred_period.get('End')}. Method: {processed_data.get('LLM_PeriodInferenceMethod')}, Notice: {processed_data.get('LLM_InferredDurationNotice')}"
            processed_data["_system_servicePeriodSource"] = f"llm_inferred_period_{llm_inferred_confidence.lower()}"
            processed_data["_system_servicePeriodInferenceConfidence"] = llm_inferred_confidence.lower()
            # Optionally store method and notice for better context if needed later
            if processed_data.get('LLM_PeriodInferenceMethod'):
                processed_data['_system_servicePeriodInferenceMethod'] = processed_data.get('LLM_PeriodInferenceMethod')
            if processed_data.get('LLM_InferredDurationNotice'):
                processed_data['_system_servicePeriodInferredDurationNotice'] = processed_data.get('LLM_InferredDurationNotice')
        elif llm_inferred_period: # Log if inferred period was present but not used (e.g. low confidence or bad format)
            logger.info(f"[{file_name_for_logging}] LLM_InferredServicePeriod was present but not used. Data: {llm_inferred_period}, Confidence: {llm_inferred_confidence}")


    if chosen_period:
        start_date_str = chosen_period.get("Start")
        end_date_str = chosen_period.get("End")
        # Basic validation: ensure they are strings (further validation happens in main.py's _parse_date_string_to_date)
        if isinstance(start_date_str, str) and isinstance(end_date_str, str):
            processed_data["ExpectedServiceStartDate"] = start_date_str
            processed_data["ExpectedServiceEndDate"] = end_date_str
            logger.info(f"[{file_name_for_logging}] Set ExpectedServiceStartDate/EndDate from {period_source_log_msg}")
        else:
             logger.warning(f"[{file_name_for_logging}] Chosen period contained non-string Start/End values: {chosen_period}. Expected dates were not set.")
    else:
        logger.info(f"[{file_name_for_logging}] No valid explicit or confidently inferred ServicePeriod/BillingPeriod found in LLM data to set ExpectedServiceStartDate/EndDate.")
    # --- End Expected Dates ---

    return processed_data

# Helper to check if direct OpenAI attempt was sufficient
def is_direct_openai_attempt_sufficient(validated_data: Optional[Dict[str, Any]]) -> bool:
    if not validated_data:
        logger.info("Direct OpenAI attempt: No validated data returned. Deemed insufficient.")
        return False

    # Check for presence of critical fields (InvoiceNumber, Date, Total)
    critical_fields = ["InvoiceNumber", "Date", "Total"]
    missing_critical_fields = [field for field in critical_fields if not validated_data.get(field)]
    if missing_critical_fields:
        logger.info(f"Direct OpenAI attempt: Missing critical fields: {', '.join(missing_critical_fields)}. Deemed insufficient.")
        return False

    # Check TotalValidation match status
    total_validation = validated_data.get("System_TotalValidation", {})
    match_status = total_validation.get("match")

    if match_status is False:
        logger.info("Direct OpenAI attempt result: System_TotalValidation match is FALSE. Deemed insufficient.")
        return False
    
    # If we reach here, critical fields are present and total doesn't mismatch (or was None, meaning Xero total was missing).
    # The decision to fallback based on missing dates for prepayment assessment will be handled in the calling function (main.py).
    logger.info("Direct OpenAI attempt result: Critical fields present and Total does not mismatch (or Xero Total was None). Considered provisionally sufficient by this check.")
    return True 